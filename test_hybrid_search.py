"""
Test script for the ChromaDB + MongoDB Hybrid Search System.

This script demonstrates the two-stage search process:
1. MongoDB search excluding embedding factors
2. ChromaDB vector search on filtered results
3. OpenAI query processing and result ranking
"""

import asyncio
import json
from chromaMongoSearch import ChromaMongoSearchEngine, search_resumes

async def test_basic_search():
    """Test basic search functionality."""
    
    print("🔍 Testing Basic Hybrid Search")
    print("=" * 60)
    
    # Test queries
    test_queries = [
        "Find software engineers with Python experience",
        "Looking for data scientists with machine learning background",
        "Need frontend developers with React and JavaScript skills",
        "Search for project managers with agile methodology experience",
        "Find candidates with biotechnology and microbiology background"
    ]
    
    search_engine = ChromaMongoSearchEngine()
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 Test {i}: {query}")
        print("-" * 50)
        
        try:
            results = await search_engine.search(query, top_k=5)
            
            # Display results summary
            print(f"✅ Search completed successfully")
            print(f"   Original Query: {results.get('query')}")
            print(f"   Optimized Query: {results.get('optimized_query')}")
            print(f"   MongoDB IDs Found: {results.get('mongodb_ids_found', 0)}")
            print(f"   ChromaDB Docs Found: {results.get('chromadb_docs_found', 0)}")
            print(f"   Total Results: {results.get('total_results', 0)}")
            
            # Show top results
            if results.get('results'):
                print(f"\n   🏆 Top {min(3, len(results['results']))} Results:")
                for result in results['results'][:3]:
                    print(f"      {result['rank']}. {result['full_name']} ({result['embedding_type']})")
                    print(f"         Similarity: {result['similarity_score']:.3f} | Distance: {result['match_distance']:.3f}")
                    print(f"         MongoDB ID: {result['mongodb_id']}")
                    print(f"         File: {result['original_filename']}")
                    print()
            else:
                print("   ⚠️  No results found")
            
            if results.get('error'):
                print(f"   ❌ Error: {results['error']}")
                
        except Exception as e:
            print(f"   ❌ Test failed: {e}")
            import traceback
            traceback.print_exc()

async def test_search_stages():
    """Test individual search stages."""
    
    print("\n🔬 Testing Individual Search Stages")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    test_query = "Find Python developers with machine learning experience"
    
    print(f"Test Query: {test_query}")
    print()
    
    try:
        # Stage 1: OpenAI query processing
        print("📋 Stage 1: OpenAI Query Processing")
        optimized_query = await search_engine.process_nlp_query_with_openai(test_query)
        print(f"   Original: {test_query}")
        print(f"   Optimized: {optimized_query}")
        print("   ✅ Stage 1 completed")
        
        # Stage 2: MongoDB search
        print("\n📋 Stage 2: MongoDB Search (excluding embedding fields)")
        mongodb_ids = search_engine.search_mongodb_excluding_embedding_fields(limit=100)
        print(f"   Found {len(mongodb_ids)} MongoDB IDs")
        if mongodb_ids:
            print(f"   Sample IDs: {mongodb_ids[:3]}...")
        print("   ✅ Stage 2 completed")
        
        # Stage 3: ChromaDB filtering
        print("\n📋 Stage 3: ChromaDB Document Filtering")
        chromadb_results = search_engine.get_chromadb_docs_by_mongodb_ids(mongodb_ids[:50])  # Limit for testing
        print(f"   Found {len(chromadb_results.get('ids', []))} ChromaDB documents")
        if chromadb_results.get('metadatas'):
            embedding_types = set(meta.get('embedding_type', 'Unknown') for meta in chromadb_results['metadatas'])
            print(f"   Embedding types: {list(embedding_types)}")
        print("   ✅ Stage 3 completed")
        
        # Stage 4: Query embedding
        print("\n📋 Stage 4: Query Embedding Generation")
        query_embedding = search_engine.get_embedding(optimized_query)
        if query_embedding:
            print(f"   Generated embedding vector (length: {len(query_embedding)})")
            print(f"   Sample values: {query_embedding[:5]}...")
            print("   ✅ Stage 4 completed")
        else:
            print("   ❌ Stage 4 failed")
            return
        
        # Stage 5: Vector search
        print("\n📋 Stage 5: Vector Search")
        search_results = search_engine.perform_vector_search(query_embedding, chromadb_results, top_k=5)
        print(f"   Found {len(search_results)} vector search results")
        if search_results:
            print("   Top result:")
            top_result = search_results[0]
            print(f"      Name: {top_result['full_name']}")
            print(f"      Similarity: {top_result['similarity_score']:.3f}")
            print(f"      Embedding Type: {top_result['embedding_type']}")
        print("   ✅ Stage 5 completed")
        
        print("\n🎉 All stages completed successfully!")
        
    except Exception as e:
        print(f"❌ Stage testing failed: {e}")
        import traceback
        traceback.print_exc()

async def test_convenience_function():
    """Test the convenience search function."""
    
    print("\n🚀 Testing Convenience Function")
    print("=" * 60)
    
    query = "Find experienced software developers with Python and AI skills"
    print(f"Query: {query}")
    
    try:
        results = await search_resumes(query, top_k=3)
        
        print(f"\n📊 Results Summary:")
        print(f"   Total Results: {results.get('total_results', 0)}")
        print(f"   MongoDB IDs: {results.get('mongodb_ids_found', 0)}")
        print(f"   ChromaDB Docs: {results.get('chromadb_docs_found', 0)}")
        
        if results.get('results'):
            print(f"\n🏆 Top Results:")
            for result in results['results']:
                print(f"   {result['rank']}. {result['full_name']}")
                print(f"      Similarity: {result['similarity_score']:.3f}")
                print(f"      Type: {result['embedding_type']}")
                print(f"      Preview: {result['document_preview'][:100]}...")
                print()
        
        if results.get('search_stages'):
            print(f"\n🔄 Search Stages:")
            for stage, description in results['search_stages'].items():
                print(f"   {stage}: {description}")
        
        print("✅ Convenience function test completed")
        
    except Exception as e:
        print(f"❌ Convenience function test failed: {e}")
        import traceback
        traceback.print_exc()

def test_search_engine_initialization():
    """Test search engine initialization."""
    
    print("🔧 Testing Search Engine Initialization")
    print("=" * 60)
    
    try:
        # Test basic initialization
        search_engine = ChromaMongoSearchEngine()
        print("✅ Basic initialization successful")
        print(f"   Database: {search_engine.database_name}")
        print(f"   Collection: {search_engine.collection_name}")
        print(f"   Embedding columns: {len(search_engine.embedding_columns)}")
        print(f"   Excluded MongoDB fields: {len(search_engine.excluded_mongodb_fields)}")
        
        # Test with custom parameters
        custom_engine = ChromaMongoSearchEngine(
            database_name="test_db",
            collection_name="test_collection",
            chroma_host="localhost",
            chroma_port=8000
        )
        print("✅ Custom initialization successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    
    print("🔬 ChromaDB + MongoDB Hybrid Search Test Suite")
    print("=" * 80)
    
    # Test 1: Initialization
    init_success = test_search_engine_initialization()
    
    if not init_success:
        print("❌ Initialization failed. Stopping tests.")
        return
    
    # Test 2: Individual stages
    await test_search_stages()
    
    # Test 3: Basic search
    await test_basic_search()
    
    # Test 4: Convenience function
    await test_convenience_function()
    
    print("\n" + "=" * 80)
    print("🏁 Test Suite Completed!")
    print("\nKey Features Tested:")
    print("✅ Two-stage search process (MongoDB → ChromaDB)")
    print("✅ OpenAI query optimization")
    print("✅ Vector search with similarity scores")
    print("✅ MongoDB ID filtering in ChromaDB")
    print("✅ Comprehensive result formatting")
    print("✅ Error handling and logging")

if __name__ == "__main__":
    asyncio.run(main())
