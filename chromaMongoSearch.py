"""
ChromaDB + MongoDB Hybrid Search System

This module implements a two-stage search process:
1. MongoDB search excluding embedding factors to get MongoDB IDs
2. ChromaDB vector search on those specific MongoDB IDs using OpenAI embeddings
3. Return results with match distances

Usage:
    from chromaMongoSearch import ChromaMongoSearchEngine
    
    search_engine = ChromaMongoSearchEngine()
    results = await search_engine.search("Find software engineers with Python experience")
"""

import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from openai import OpenAI
from chromadb import HttpClient
from helperMongoDb import MongoDBClient
from bson import ObjectId
import asyncio

class ChromaMongoSearchEngine:
    """
    Hybrid search engine that combines MongoDB filtering with ChromaDB vector search.
    """
    
    def __init__(self,
                 database_name: str = "dbProductionV2",
                 collection_name: str = "collectionResumeV2",
                 chroma_host: str = "localhost",
                 chroma_port: int = 8000,
                 chroma_collection: str = "resumes_by_type",
                 openai_api_key: Optional[str] = None,
                 openai_base_url: Optional[str] = None):
        """
        Initialize the hybrid search engine.
        
        Args:
            database_name: MongoDB database name
            collection_name: MongoDB collection name
            chroma_host: ChromaDB server host
            chroma_port: ChromaDB server port
            chroma_collection: ChromaDB collection name
            openai_api_key: OpenAI API key (optional)
            openai_base_url: OpenAI base URL (optional)
        """
        self.database_name = database_name
        self.collection_name = collection_name
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Initialize MongoDB client
        self.mongo_client = MongoDBClient(db_name=database_name)
        
        # Initialize ChromaDB client
        self.chroma_client = HttpClient(host=chroma_host, port=chroma_port)
        self.chroma_collection = self.chroma_client.get_or_create_collection(chroma_collection)
        
        # Initialize OpenAI client
        if openai_api_key and openai_base_url:
            self.openai_client = OpenAI(api_key=openai_api_key, base_url=openai_base_url)
        else:
            # Use default OpenAI configuration
            self.openai_client = OpenAI()
        
        # Define embedding columns for vector search (15 total as requested)
        self.embedding_columns = [
            "FullNameEmbedding",
            "InstitutionEmbedding",
            "CompanyNameEmbedding",
            "RoleEmbedding",
            "Description/ResponsibilityEmbedding",
            "SkillsEmbedding",
            "CertificationNameEmbedding",
            "IssuingOrganizationEmbedding",
            "AchievementNameEmbedding",
            "ProjectNameEmbedding",
            "DescriptionEmbedding",
            "TechnologiesUsedEmbedding",
            "ProjectRoleEmbedding"
        ]

        # Map embedding columns to their corresponding embedding types in ChromaDB
        self.embedding_type_mapping = {
            "FullNameEmbedding": "FullName",
            "InstitutionEmbedding": "Institution",
            "CompanyNameEmbedding": "CompanyName",
            "RoleEmbedding": "Role",
            "Description/ResponsibilityEmbedding": "Description/Responsibility",
            "SkillsEmbedding": "Skills",
            "CertificationNameEmbedding": "CertificationName",
            "IssuingOrganizationEmbedding": "IssuingOrganization",
            "AchievementNameEmbedding": "AchievementName",
            "ProjectNameEmbedding": "ProjectName",
            "DescriptionEmbedding": "Description",
            "TechnologiesUsedEmbedding": "TechnologiesUsed",
            "ProjectRoleEmbedding": "ProjectRole"
        }
        
        # Define fields to exclude from MongoDB search (corresponding to embedding columns)
        self.excluded_mongodb_fields = [
            "Resume.PersonalInformation.FullName",
            "Resume.Education.Institution",
            "Resume.WorkExperience.CompanyName", 
            "Resume.WorkExperience.Role",
            "Resume.WorkExperience.Description/Responsibility",
            "Resume.Skills",
            "Resume.Certifications.CertificationName",
            "Resume.Certifications.IssuingOrganization",
            "Resume.Achievements.AchievementName",
            "Resume.Projects.ProjectName",
            "Resume.Projects.Description",
            "Resume.Projects.TechnologiesUsed",
            "Resume.Projects.Role"
        ]

    def get_embedding(self, text: str) -> Optional[List[float]]:
        """
        Generate embedding for the given text using OpenAI.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector or None if failed
        """
        if not text or not isinstance(text, str) or not text.strip():
            return None
        try:
            response = self.openai_client.embeddings.create(
                input=text.strip(),
                model="text-embedding-3-small"
            )
            return response.data[0].embedding
        except Exception as e:
            self.logger.error(f"Embedding error for '{text[:40]}...': {e}")
            return None

    def search_mongodb_excluding_embedding_fields(self, limit: int = 1000) -> List[str]:
        """
        Search MongoDB for all resumes, excluding embedding-related fields from consideration.
        This gets all MongoDB IDs for resumes that exist, regardless of embedding field content.
        
        Args:
            limit: Maximum number of results to return
            
        Returns:
            List of MongoDB IDs as strings
        """
        try:
            collection = self.mongo_client.db[self.collection_name]
            
            # Get all documents but only return the _id field
            # We're not filtering by embedding fields, just getting all available resumes
            cursor = collection.find(
                {},  # Empty filter - get all documents
                {"_id": 1}  # Only return the _id field
            ).limit(limit)
            
            mongodb_ids = [str(doc["_id"]) for doc in cursor]
            
            self.logger.info(f"Found {len(mongodb_ids)} MongoDB IDs for ChromaDB search")
            return mongodb_ids
            
        except Exception as e:
            self.logger.error(f"Error searching MongoDB: {e}")
            return []

    def get_chromadb_docs_by_mongodb_ids(self, mongodb_ids: List[str]) -> Dict[str, Any]:
        """
        Get ChromaDB documents filtered by MongoDB IDs.
        
        Args:
            mongodb_ids: List of MongoDB IDs to filter by
            
        Returns:
            ChromaDB query results
        """
        try:
            if not mongodb_ids:
                return {"ids": [], "metadatas": [], "documents": [], "embeddings": []}
            
            # Query ChromaDB for documents with these MongoDB IDs
            results = self.chroma_collection.get(
                where={"mongodb_id": {"$in": mongodb_ids}},
                include=["metadatas", "documents", "embeddings"]
            )
            
            self.logger.info(f"Found {len(results.get('ids', []))} ChromaDB documents for {len(mongodb_ids)} MongoDB IDs")
            return results
            
        except Exception as e:
            self.logger.error(f"Error querying ChromaDB: {e}")
            return {"ids": [], "metadatas": [], "documents": [], "embeddings": []}

    async def process_nlp_query_with_openai(self, query: str) -> str:
        """
        Process the NLP query using OpenAI to create a better search query.
        
        Args:
            query: Natural language query from user
            
        Returns:
            Processed query optimized for vector search
        """
        try:
            system_prompt = """
            You are a resume search query optimizer. Your task is to take a natural language query 
            and convert it into a concise, keyword-rich search query that would work well for 
            semantic vector search on resume data.
            
            Focus on:
            - Job titles and roles
            - Skills and technologies
            - Experience levels
            - Education and certifications
            - Industry domains
            - Project types
            
            Return only the optimized search query, nothing else.
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Optimize this resume search query: {query}"}
                ],
                temperature=0.3
            )
            
            optimized_query = response.choices[0].message.content.strip()
            self.logger.info(f"Optimized query: '{query}' -> '{optimized_query}'")
            return optimized_query
            
        except Exception as e:
            self.logger.error(f"Error processing query with OpenAI: {e}")
            return query  # Return original query if processing fails

    def perform_vector_search(self, query_embedding: List[float], 
                            chromadb_results: Dict[str, Any], 
                            top_k: int = 10) -> List[Dict[str, Any]]:
        """
        Perform vector search on ChromaDB results using query embedding.
        
        Args:
            query_embedding: Embedding vector for the search query
            chromadb_results: Results from ChromaDB filtered by MongoDB IDs
            top_k: Number of top results to return
            
        Returns:
            List of search results with match distances
        """
        try:
            if not chromadb_results.get("ids"):
                return []
            
            # Perform similarity search using the query embedding
            search_results = self.chroma_collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                include=["metadatas", "documents", "distances"],
                where={"mongodb_id": {"$in": [meta.get("mongodb_id") for meta in chromadb_results.get("metadatas", [])]}}
            )
            
            # Format results
            formatted_results = []
            if search_results.get("ids") and search_results["ids"][0]:
                for i, (doc_id, metadata, document, distance) in enumerate(zip(
                    search_results["ids"][0],
                    search_results["metadatas"][0], 
                    search_results["documents"][0],
                    search_results["distances"][0]
                )):
                    formatted_results.append({
                        "rank": i + 1,
                        "chromadb_id": doc_id,
                        "mongodb_id": metadata.get("mongodb_id"),
                        "full_name": metadata.get("FullName", "N/A"),
                        "email": metadata.get("Email", "N/A"),
                        "embedding_type": metadata.get("embedding_type", "N/A"),
                        "match_distance": distance,
                        "similarity_score": 1 - distance,  # Convert distance to similarity
                        "document_preview": document[:200] + "..." if len(document) > 200 else document,
                        "original_filename": metadata.get("original_filename", "N/A")
                    })
            
            self.logger.info(f"Vector search returned {len(formatted_results)} results")
            return formatted_results
            
        except Exception as e:
            self.logger.error(f"Error performing vector search: {e}")
            return []

    async def search(self, nlp_query: str, top_k: int = 10, mongodb_limit: int = 1000) -> Dict[str, Any]:
        """
        Main search function that orchestrates the hybrid search process.
        
        Args:
            nlp_query: Natural language search query
            top_k: Number of top results to return
            mongodb_limit: Maximum MongoDB documents to consider
            
        Returns:
            Search results with metadata and match distances
        """
        try:
            self.logger.info(f"Starting hybrid search for query: '{nlp_query}'")
            
            # Stage 1: Process NLP query with OpenAI
            self.logger.info("Stage 1: Processing NLP query with OpenAI...")
            optimized_query = await self.process_nlp_query_with_openai(nlp_query)
            
            # Stage 2: Get MongoDB IDs (excluding embedding fields from search criteria)
            self.logger.info("Stage 2: Getting MongoDB IDs...")
            mongodb_ids = self.search_mongodb_excluding_embedding_fields(limit=mongodb_limit)
            
            if not mongodb_ids:
                return {
                    "query": nlp_query,
                    "optimized_query": optimized_query,
                    "total_results": 0,
                    "results": [],
                    "error": "No MongoDB documents found"
                }
            
            # Stage 3: Get ChromaDB documents for these MongoDB IDs
            self.logger.info("Stage 3: Filtering ChromaDB by MongoDB IDs...")
            chromadb_results = self.get_chromadb_docs_by_mongodb_ids(mongodb_ids)
            
            if not chromadb_results.get("ids"):
                return {
                    "query": nlp_query,
                    "optimized_query": optimized_query,
                    "mongodb_ids_found": len(mongodb_ids),
                    "total_results": 0,
                    "results": [],
                    "error": "No ChromaDB documents found for MongoDB IDs"
                }
            
            # Stage 4: Generate embedding for optimized query
            self.logger.info("Stage 4: Generating query embedding...")
            query_embedding = self.get_embedding(optimized_query)
            
            if not query_embedding:
                return {
                    "query": nlp_query,
                    "optimized_query": optimized_query,
                    "mongodb_ids_found": len(mongodb_ids),
                    "chromadb_docs_found": len(chromadb_results.get("ids", [])),
                    "total_results": 0,
                    "results": [],
                    "error": "Failed to generate query embedding"
                }
            
            # Stage 5: Perform vector search
            self.logger.info("Stage 5: Performing vector search...")
            search_results = self.perform_vector_search(query_embedding, chromadb_results, top_k)
            
            # Return comprehensive results
            return {
                "query": nlp_query,
                "optimized_query": optimized_query,
                "mongodb_ids_found": len(mongodb_ids),
                "chromadb_docs_found": len(chromadb_results.get("ids", [])),
                "total_results": len(search_results),
                "results": search_results,
                "search_stages": {
                    "stage_1": "NLP query processed with OpenAI",
                    "stage_2": f"Found {len(mongodb_ids)} MongoDB IDs",
                    "stage_3": f"Found {len(chromadb_results.get('ids', []))} ChromaDB documents",
                    "stage_4": "Query embedding generated",
                    "stage_5": f"Vector search returned {len(search_results)} results"
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error in hybrid search: {e}")
            return {
                "query": nlp_query,
                "total_results": 0,
                "results": [],
                "error": str(e)
            }

# Utility functions for easy usage
async def search_resumes(query: str, top_k: int = 10) -> Dict[str, Any]:
    """
    Convenience function to search resumes using the hybrid approach.
    
    Args:
        query: Natural language search query
        top_k: Number of top results to return
        
    Returns:
        Search results dictionary
    """
    search_engine = ChromaMongoSearchEngine()
    return await search_engine.search(query, top_k)

# Example usage
if __name__ == "__main__":
    async def main():
        # Example searches
        queries = [
            "Find fema",
            "Looking for data scientists with machine learning skills",
            "Need frontend developers with React experience",
            "Search for project managers with agile experience"
        ]
        
        search_engine = ChromaMongoSearchEngine()
        
        for query in queries:
            print(f"\n{'='*60}")
            print(f"Searching: {query}")
            print('='*60)
            
            results = await search_engine.search(query, top_k=5)
            
            print(f"Original Query: {results.get('query')}")
            print(f"Optimized Query: {results.get('optimized_query')}")
            print(f"MongoDB IDs Found: {results.get('mongodb_ids_found', 0)}")
            print(f"ChromaDB Docs Found: {results.get('chromadb_docs_found', 0)}")
            print(f"Total Results: {results.get('total_results', 0)}")
            
            if results.get('results'):
                print("\nTop Results:")
                for result in results['results'][:3]:  # Show top 3
                    print(f"  {result['rank']}. {result['full_name']} ({result['embedding_type']})")
                    print(f"     Similarity: {result['similarity_score']:.3f}")
                    print(f"     MongoDB ID: {result['mongodb_id']}")
                    print(f"     Preview: {result['document_preview'][:100]}...")
                    print()
            
            if results.get('error'):
                print(f"Error: {results['error']}")
    
    asyncio.run(main())
