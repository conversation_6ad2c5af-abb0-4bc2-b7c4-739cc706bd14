"""
ChromaDB + MongoDB Hybrid Search System

This module implements a two-stage search process:
1. MongoDB search excluding embedding factors to get MongoDB IDs
2. ChromaDB vector search on those specific MongoDB IDs using OpenAI embeddings
3. Return results with match distances

Usage:
    from chromaMongoSearch import ChromaMongoSearchEngine
    
    search_engine = ChromaMongoSearchEngine()
    results = await search_engine.search("Find software engineers with Python experience")
"""

import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from openai import OpenAI
from chromadb import HttpClient
from helperMongoDb import MongoDBClient
from bson import ObjectId
import asyncio

class ChromaMongoSearchEngine:
    """
    Hybrid search engine that combines MongoDB filtering with ChromaDB vector search.
    """
    
    def __init__(self,
                 database_name: str = "dbProductionV2",
                 collection_name: str = "collectionResumeV2",
                 chroma_host: str = "localhost",
                 chroma_port: int = 8000,
                 chroma_collection: str = "resumes_by_type",
                 openai_api_key: Optional[str] = None,
                 openai_base_url: Optional[str] = None):
        """
        Initialize the hybrid search engine.
        
        Args:
            database_name: MongoDB database name
            collection_name: MongoDB collection name
            chroma_host: ChromaDB server host
            chroma_port: ChromaDB server port
            chroma_collection: ChromaDB collection name
            openai_api_key: OpenAI API key (optional)
            openai_base_url: OpenAI base URL (optional)
        """
        self.database_name = database_name
        self.collection_name = collection_name
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Initialize MongoDB client
        self.mongo_client = MongoDBClient(db_name=database_name)
        
        # Initialize ChromaDB client
        self.chroma_client = HttpClient(host=chroma_host, port=chroma_port)
        self.chroma_collection = self.chroma_client.get_or_create_collection(chroma_collection)
        
        # Initialize OpenAI client
        if openai_api_key and openai_base_url:
            self.openai_client = OpenAI(api_key=openai_api_key, base_url=openai_base_url)
        else:
            # Use default OpenAI configuration
            self.openai_client = OpenAI()
        
        # Define embedding columns for vector search (15 total as requested)
        self.embedding_columns = [
            "FullNameEmbedding",
            "InstitutionEmbedding",
            "CompanyNameEmbedding",
            "RoleEmbedding",
            "Description/ResponsibilityEmbedding",
            "SkillsEmbedding",
            "CertificationNameEmbedding",
            "IssuingOrganizationEmbedding",
            "AchievementNameEmbedding",
            "ProjectNameEmbedding",
            "DescriptionEmbedding",
            "TechnologiesUsedEmbedding",
            "ProjectRoleEmbedding"
        ]

        # Map embedding columns to their corresponding embedding types in ChromaDB
        self.embedding_type_mapping = {
            "FullNameEmbedding": "FullName",
            "InstitutionEmbedding": "Institution",
            "CompanyNameEmbedding": "CompanyName",
            "RoleEmbedding": "Role",
            "Description/ResponsibilityEmbedding": "Description/Responsibility",
            "SkillsEmbedding": "Skills",
            "CertificationNameEmbedding": "CertificationName",
            "IssuingOrganizationEmbedding": "IssuingOrganization",
            "AchievementNameEmbedding": "AchievementName",
            "ProjectNameEmbedding": "ProjectName",
            "DescriptionEmbedding": "Description",
            "TechnologiesUsedEmbedding": "TechnologiesUsed",
            "ProjectRoleEmbedding": "ProjectRole"
        }
        
        # Define fields to exclude from MongoDB search (corresponding to embedding columns)
        self.excluded_mongodb_fields = [
            "Resume.PersonalInformation.FullName",
            "Resume.Education.Institution",
            "Resume.WorkExperience.CompanyName", 
            "Resume.WorkExperience.Role",
            "Resume.WorkExperience.Description/Responsibility",
            "Resume.Skills",
            "Resume.Certifications.CertificationName",
            "Resume.Certifications.IssuingOrganization",
            "Resume.Achievements.AchievementName",
            "Resume.Projects.ProjectName",
            "Resume.Projects.Description",
            "Resume.Projects.TechnologiesUsed",
            "Resume.Projects.Role"
        ]

    def get_embedding(self, text: str) -> Optional[List[float]]:
        """
        Generate embedding for the given text using OpenAI.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector or None if failed
        """
        if not text or not isinstance(text, str) or not text.strip():
            return None
        try:
            response = self.openai_client.embeddings.create(
                input=text.strip(),
                model="text-embedding-3-small"
            )
            return response.data[0].embedding
        except Exception as e:
            self.logger.error(f"Embedding error for '{text[:40]}...': {e}")
            return None

    async def search_mongodb_with_generated_query(self, mongodb_query: Dict[str, Any], limit: int = 1000) -> List[str]:
        """
        Search MongoDB using the generated query on NON-embedding fields only.

        Args:
            mongodb_query: Generated MongoDB query dictionary
            limit: Maximum number of results to return

        Returns:
            List of MongoDB IDs as strings
        """
        try:
            collection = self.mongo_client.db[self.collection_name]

            # Use the generated MongoDB query, or fallback to all documents
            search_filter = mongodb_query if mongodb_query else {}

            # Get documents but only return the _id field
            cursor = collection.find(
                search_filter,
                {"_id": 1}
            ).limit(limit)

            mongodb_ids = [str(doc["_id"]) for doc in cursor]

            self.logger.info(f"Found {len(mongodb_ids)} MongoDB IDs using generated query: {search_filter}")
            return mongodb_ids

        except Exception as e:
            self.logger.error(f"Error searching MongoDB with generated query: {e}")
            # Fallback: return all document IDs
            try:
                cursor = collection.find({}, {"_id": 1}).limit(limit)
                mongodb_ids = [str(doc["_id"]) for doc in cursor]
                self.logger.info(f"Fallback: returning {len(mongodb_ids)} MongoDB IDs")
                return mongodb_ids
            except Exception as fallback_error:
                self.logger.error(f"Fallback search also failed: {fallback_error}")
                return []

    def get_chromadb_docs_by_mongodb_ids(self, mongodb_ids: List[str]) -> Dict[str, Any]:
        """
        Get ChromaDB documents filtered by MongoDB IDs, focusing only on the 13 embedding fields.

        Args:
            mongodb_ids: List of MongoDB IDs to filter by

        Returns:
            ChromaDB query results filtered to only the 13 embedding types
        """
        try:
            if not mongodb_ids:
                return {"ids": [], "metadatas": [], "documents": [], "embeddings": []}

            # Define the 13 embedding types we want to search (without "Embedding" suffix)
            target_embedding_types = [
                "FullName",
                "Institution",
                "CompanyName",
                "Role",
                "Description/Responsibility",
                "Skills",
                "CertificationName",
                "IssuingOrganization",
                "AchievementName",
                "ProjectName",
                "Description",
                "TechnologiesUsed",
                "ProjectRole"
            ]

            # Query ChromaDB for documents with these MongoDB IDs AND specific embedding types
            results = self.chroma_collection.get(
                where={
                    "$and": [
                        {"mongodb_id": {"$in": mongodb_ids}},
                        {"embedding_type": {"$in": target_embedding_types}}
                    ]
                },
                include=["metadatas", "documents", "embeddings"]
            )

            self.logger.info(f"Found {len(results.get('ids', []))} ChromaDB documents for {len(mongodb_ids)} MongoDB IDs (13 embedding types only)")
            return results

        except Exception as e:
            self.logger.error(f"Error querying ChromaDB: {e}")
            return {"ids": [], "metadatas": [], "documents": [], "embeddings": []}

    async def generate_mongodb_search_query(self, user_query: str) -> Dict[str, Any]:
        """
        First OpenAI call: Generate MongoDB search query focusing on NON-embedding fields.

        Args:
            user_query: Natural language query from user

        Returns:
            MongoDB query dictionary
        """
        try:
            system_prompt = """
            You are a MongoDB query generator for resume search. Your task is to convert a natural language query
            into a MongoDB query that searches ONLY on non-embedding fields.

            Available non-embedding fields to search:
            - Resume.PersonalInformation.Gender (Male/Female)
            - Resume.PersonalInformation.Email
            - Resume.PersonalInformation.Address, City, State
            - Resume.PersonalInformation.ContactNumber
            - Resume.PersonalInformation.BirthDate
            - Resume.PersonalInformation.Objective
            - Resume.Education.Degree, Specialization, GraduationYear, Percentage
            - Resume.WorkExperience.StartYear, EndYear, Duration, Location
            - Resume.TotalWorkExperienceInYears
            - Resume.Certifications.IssueDate, ExpiryDate
            - Resume.Languages
            - Resume.Achievements.Date
            - Resume.Projects.Duration, TeamSize

            DO NOT search on these embedding fields:
            - FullName, Institution, CompanyName, Role, Description/Responsibility
            - Skills, CertificationName, IssuingOrganization, AchievementName
            - ProjectName, Description, TechnologiesUsed, ProjectRole

            Return a valid MongoDB query in JSON format. Use $regex for text matching and $and/$or for combinations.

            Examples:
            - "Find females" -> {"Resume.PersonalInformation.Gender": {"$regex": "female", "$options": "i"}}
            - "Teachers with degree" -> {"Resume.Education.Degree": {"$regex": "education|teacher|bed", "$options": "i"}}
            - "5+ years experience" -> {"Resume.TotalWorkExperienceInYears": {"$regex": "^P[5-9]Y|^P[1-9][0-9]Y"}}
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Generate MongoDB query for: {user_query}"}
                ],
                temperature=0.1
            )

            query_text = response.choices[0].message.content.strip()

            # Try to parse as JSON
            try:
                import json
                mongodb_query = json.loads(query_text)
                self.logger.info(f"Generated MongoDB query: {mongodb_query}")
                return mongodb_query
            except json.JSONDecodeError:
                # Fallback to simple text search
                self.logger.warning(f"Could not parse MongoDB query, using fallback: {query_text}")
                return {}

        except Exception as e:
            self.logger.error(f"Error generating MongoDB query: {e}")
            return {}

    async def generate_chromadb_search_query(self, user_query: str) -> str:
        """
        Second OpenAI call: Generate ChromaDB search query focusing on the 13 embedding fields.

        Args:
            user_query: Original natural language query from user

        Returns:
            Optimized query for vector search on embedding fields
        """
        try:
            system_prompt = """
            You are a ChromaDB vector search query optimizer. Your task is to take a natural language query
            and convert it into a search query optimized for semantic vector search on these 13 resume embedding fields:

            1. FullName - Person's name
            2. Institution - Educational institutions
            3. CompanyName - Company/organization names
            4. Role - Job titles and positions
            5. Description/Responsibility - Job responsibilities and descriptions
            6. Skills - Technical and soft skills
            7. CertificationName - Names of certifications
            8. IssuingOrganization - Organizations that issued certifications
            9. AchievementName - Names of achievements and awards
            10. ProjectName - Names of projects
            11. Description - Project descriptions
            12. TechnologiesUsed - Technologies and tools used in projects
            13. ProjectRole - Role in projects

            Focus on extracting keywords related to:
            - Job titles, roles, positions
            - Skills, technologies, programming languages
            - Company names, institutions
            - Project types and descriptions
            - Certifications and achievements
            - Technical expertise areas

            Return a concise, keyword-rich query optimized for semantic similarity search.
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Create vector search query for: {user_query}"}
                ],
                temperature=0.3
            )

            vector_query = response.choices[0].message.content.strip()
            self.logger.info(f"Generated vector search query: '{user_query}' -> '{vector_query}'")
            return vector_query

        except Exception as e:
            self.logger.error(f"Error generating vector search query: {e}")
            return user_query  # Return original query if processing fails

    async def verify_mongodb_filters(self, mongodb_ids: List[str], original_query: str) -> List[str]:
        """
        Verify that MongoDB IDs still match the original query filters.
        This is a post-processing step to ensure consistency.

        Args:
            mongodb_ids: List of MongoDB IDs to verify
            original_query: Original query to extract filters from

        Returns:
            Filtered list of MongoDB IDs that match the criteria
        """
        try:
            filters = self.extract_filters_from_query(original_query)
            if not filters:
                return mongodb_ids  # No filters to verify

            collection = self.mongo_client.db[self.collection_name]

            # Add _id filter to the existing filters
            from bson import ObjectId
            filters["_id"] = {"$in": [ObjectId(id_str) for id_str in mongodb_ids]}

            # Find documents that match both the ID list and the filters
            cursor = collection.find(filters, {"_id": 1})
            verified_ids = [str(doc["_id"]) for doc in cursor]

            self.logger.info(f"Verified {len(verified_ids)} out of {len(mongodb_ids)} MongoDB IDs against filters")
            return verified_ids

        except Exception as e:
            self.logger.error(f"Error verifying MongoDB filters: {e}")
            return mongodb_ids  # Return original list if verification fails

    def perform_vector_search(self, query_embedding: List[float],
                            chromadb_results: Dict[str, Any],
                            top_k: int = 10) -> List[Dict[str, Any]]:
        """
        Perform vector search on ChromaDB results using query embedding.
        
        Args:
            query_embedding: Embedding vector for the search query
            chromadb_results: Results from ChromaDB filtered by MongoDB IDs
            top_k: Number of top results to return
            
        Returns:
            List of search results with match distances
        """
        try:
            if not chromadb_results.get("ids"):
                return []
            
            # Perform similarity search using the query embedding
            search_results = self.chroma_collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                include=["metadatas", "documents", "distances"],
                where={"mongodb_id": {"$in": [meta.get("mongodb_id") for meta in chromadb_results.get("metadatas", [])]}}
            )
            
            # Format results
            formatted_results = []
            if search_results.get("ids") and search_results["ids"][0]:
                for i, (doc_id, metadata, document, distance) in enumerate(zip(
                    search_results["ids"][0],
                    search_results["metadatas"][0], 
                    search_results["documents"][0],
                    search_results["distances"][0]
                )):
                    formatted_results.append({
                        "rank": i + 1,
                        "chromadb_id": doc_id,
                        "mongodb_id": metadata.get("mongodb_id"),
                        "full_name": metadata.get("FullName", "N/A"),
                        "email": metadata.get("Email", "N/A"),
                        "embedding_type": metadata.get("embedding_type", "N/A"),
                        "match_distance": distance,
                        "similarity_score": 1 - distance,  # Convert distance to similarity
                        "document_preview": document[:200] + "..." if len(document) > 200 else document,
                        "original_filename": metadata.get("original_filename", "N/A")
                    })
            
            self.logger.info(f"Vector search returned {len(formatted_results)} results")
            return formatted_results
            
        except Exception as e:
            self.logger.error(f"Error performing vector search: {e}")
            return []

    async def search(self, nlp_query: str, top_k: int = 4, mongodb_limit: int = 1000) -> Dict[str, Any]:
        """
        Main search function that orchestrates the hybrid search process.
        
        Args:
            nlp_query: Natural language search query
            top_k: Number of top results to return
            mongodb_limit: Maximum MongoDB documents to consider
            
        Returns:
            Search results with metadata and match distances
        """
        try:
            self.logger.info(f"Starting hybrid search for query: '{nlp_query}'")

            # Stage 1: First OpenAI call - Generate MongoDB search query
            self.logger.info("Stage 1: Generating MongoDB search query with OpenAI...")
            mongodb_query = await self.generate_mongodb_search_query(nlp_query)

            # Stage 2: Search MongoDB using generated query (non-embedding fields only)
            self.logger.info("Stage 2: Searching MongoDB with generated query...")
            mongodb_ids = await self.search_mongodb_with_generated_query(mongodb_query, limit=mongodb_limit)
            
            if not mongodb_ids:
                return {
                    "query": nlp_query,
                    "mongodb_query": mongodb_query,
                    "total_results": 0,
                    "results": [],
                    "error": "No MongoDB documents found"
                }

            # Stage 3: Get ChromaDB documents for these MongoDB IDs (13 embedding fields only)
            self.logger.info("Stage 3: Filtering ChromaDB by MongoDB IDs...")
            chromadb_results = self.get_chromadb_docs_by_mongodb_ids(mongodb_ids)

            if not chromadb_results.get("ids"):
                return {
                    "query": nlp_query,
                    "mongodb_query": mongodb_query,
                    "mongodb_ids_found": len(mongodb_ids),
                    "total_results": 0,
                    "results": [],
                    "error": "No ChromaDB documents found for MongoDB IDs"
                }

            # Stage 4: Second OpenAI call - Generate ChromaDB vector search query
            self.logger.info("Stage 4: Generating ChromaDB vector search query with OpenAI...")
            vector_search_query = await self.generate_chromadb_search_query(nlp_query)

            # Stage 5: Generate embedding for vector search query
            self.logger.info("Stage 5: Generating query embedding...")
            query_embedding = self.get_embedding(vector_search_query)

            if not query_embedding:
                return {
                    "query": nlp_query,
                    "mongodb_query": mongodb_query,
                    "vector_search_query": vector_search_query,
                    "mongodb_ids_found": len(mongodb_ids),
                    "chromadb_docs_found": len(chromadb_results.get("ids", [])),
                    "total_results": 0,
                    "results": [],
                    "error": "Failed to generate query embedding"
                }

            # Stage 6: Perform vector search on the 13 embedding fields
            self.logger.info("Stage 6: Performing vector search on 13 embedding fields...")
            search_results = self.perform_vector_search(query_embedding, chromadb_results, top_k)

            # Return comprehensive results
            return {
                "query": nlp_query,
                "mongodb_query": mongodb_query,
                "vector_search_query": vector_search_query,
                "mongodb_ids_found": len(mongodb_ids),
                "chromadb_docs_found": len(chromadb_results.get("ids", [])),
                "total_results": len(search_results),
                "results": search_results,
                "search_stages": {
                    "stage_1": "MongoDB search query generated with OpenAI",
                    "stage_2": f"Found {len(mongodb_ids)} MongoDB IDs from non-embedding fields",
                    "stage_3": f"Found {len(chromadb_results.get('ids', []))} ChromaDB documents (13 embedding types)",
                    "stage_4": "ChromaDB vector search query generated with OpenAI",
                    "stage_5": "Query embedding generated",
                    "stage_6": f"Vector search returned {len(search_results)} results"
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error in hybrid search: {e}")
            return {
                "query": nlp_query,
                "total_results": 0,
                "results": [],
                "error": str(e)
            }

# Utility functions for easy usage
async def search_resumes(query: str, top_k: int = 4) -> Dict[str, Any]:
    """
    Convenience function to search resumes using the hybrid approach.
    
    Args:
        query: Natural language search query
        top_k: Number of top results to return
        
    Returns:
        Search results dictionary
    """
    search_engine = ChromaMongoSearchEngine()
    return await search_engine.search(query, top_k)

# Example usage
if __name__ == "__main__":
    async def main():
        # Example searches
        queries = [
            "Find a male engineer with chemistry experience"
        ]
        
        search_engine = ChromaMongoSearchEngine()
        
        for query in queries:
            print(f"\n{'='*60}")
            print(f"Searching: {query}")
            print('='*60)
            
            results = await search_engine.search(query, top_k=5)
            
            print(f"Original Query: {results.get('query')}")
            print(f"Optimized Query: {results.get('optimized_query')}")
            print(f"MongoDB IDs Found: {results.get('mongodb_ids_found', 0)}")
            print(f"ChromaDB Docs Found: {results.get('chromadb_docs_found', 0)}")
            print(f"Total Results: {results.get('total_results', 0)}")
            
            if results.get('results'):
                print("\nTop Results:")
                for result in results['results'][:3]:  # Show top 3
                    print(f"  {result['rank']}. {result['full_name']} ({result['embedding_type']})")
                    print(f"     Similarity: {result['similarity_score']:.3f}")
                    print(f"     MongoDB ID: {result['mongodb_id']}")
                    print(f"     Preview: {result['document_preview'][:100]}...")
                    print()
            
            if results.get('error'):
                print(f"Error: {results['error']}")
    
    asyncio.run(main())
