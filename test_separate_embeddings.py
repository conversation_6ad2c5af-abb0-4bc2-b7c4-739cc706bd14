"""
Test the separate embeddings approach to verify improved search accuracy.
"""

import asyncio
from chromaMongoSearch import ChromaMongoSearchEngine
from chromdb_processor import ChromaDBProcessor
from bson import ObjectId

async def test_separate_embeddings_processing():
    """Test that separate embeddings are being created correctly."""
    
    print("🔧 Testing Separate Embeddings Processing")
    print("=" * 60)
    
    # Create a sample resume with multiple values
    sample_resume = {
        "Resume": {
            "PersonalInformation": {
                "FullName": "<PERSON>",
                "Email": "<EMAIL>"
            },
            "WorkExperience": [
                {
                    "CompanyName": "Google",
                    "Role": "Software Engineer",
                    "Description/Responsibility": "Developed web applications using React and Node.js"
                },
                {
                    "CompanyName": "Microsoft",
                    "Role": "Senior Developer",
                    "Description/Responsibility": "Led team of 5 developers on cloud projects"
                }
            ],
            "Skills": ["Python", "JavaScript", "React", "Node.js", "AWS"],
            "Education": [
                {
                    "Institution": "Stanford University",
                    "Degree": "Computer Science"
                },
                {
                    "Institution": "MIT",
                    "Degree": "Masters"
                }
            ],
            "Projects": [
                {
                    "ProjectName": "E-commerce Platform",
                    "Description": "Built scalable e-commerce solution",
                    "TechnologiesUsed": ["React", "MongoDB", "Express"],
                    "Role": "Lead Developer"
                }
            ]
        }
    }
    
    # Process with ChromaDB
    processor = ChromaDBProcessor()
    mongodb_id = ObjectId()
    
    print(f"Processing sample resume with MongoDB ID: {mongodb_id}")
    success = processor.process_structured_data(sample_resume, mongodb_id, "test_resume.pdf")
    
    if success:
        print("✅ Successfully processed resume with separate embeddings")
        
        # Query ChromaDB to see what was stored
        results = processor.collection.get(
            where={"mongodb_id": str(mongodb_id)},
            include=["metadatas", "documents"]
        )
        
        print(f"\n📊 Stored {len(results['ids'])} separate embeddings:")
        
        # Group by embedding type
        by_type = {}
        for metadata, document in zip(results['metadatas'], results['documents']):
            emb_type = metadata['embedding_type']
            if emb_type not in by_type:
                by_type[emb_type] = []
            by_type[emb_type].append({
                'document': document,
                'value_index': metadata.get('value_index', 0)
            })
        
        # Show what was stored for each type
        for emb_type, values in by_type.items():
            print(f"\n🔹 {emb_type}: {len(values)} separate embeddings")
            for value in values:
                print(f"   [{value['value_index']}] {value['document']}")
    
    else:
        print("❌ Failed to process resume")

async def test_search_accuracy_improvement():
    """Test that separate embeddings improve search accuracy."""
    
    print("\n🎯 Testing Search Accuracy with Separate Embeddings")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Test specific searches that should benefit from separate embeddings
    test_queries = [
        "Find people who worked at Google",  # Should find specific company
        "Find Python developers",  # Should find specific skill
        "Find Stanford graduates",  # Should find specific institution
        "Find React developers",  # Should find specific technology
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 Test {i}: {query}")
        print("-" * 40)
        
        try:
            results = await search_engine.search(query, top_k=3)
            
            print(f"Embedding types searched: {results.get('embedding_types_to_search', [])}")
            print(f"Results found: {results.get('total_results', 0)}")
            
            if results.get('results'):
                print("Top matches:")
                for result in results['results']:
                    print(f"  {result['rank']}. {result['full_name']} ({result['embedding_type']})")
                    print(f"     Similarity: {result['similarity_score']:.3f}")
                    print(f"     Match: {result['document_preview'][:80]}...")
                    print()
            else:
                print("No results found")
                
        except Exception as e:
            print(f"❌ Error: {e}")

async def test_company_search_precision():
    """Test precision of company-specific searches."""
    
    print("\n🏢 Testing Company Search Precision")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Test company searches
    company_queries = [
        "Find people who worked at Google",
        "Find Microsoft employees",
        "Find Amazon developers",
        "Find people from startup companies"
    ]
    
    for query in company_queries:
        print(f"\nQuery: {query}")
        
        try:
            results = await search_engine.search(query, top_k=3)
            
            embedding_types = results.get('embedding_types_to_search', [])
            print(f"Embedding types: {embedding_types}")
            
            if "CompanyName" in embedding_types:
                print("✅ Correctly targeting CompanyName embeddings")
            else:
                print("⚠️  Not targeting CompanyName embeddings")
            
            if results.get('results'):
                print("Matches:")
                for result in results['results'][:2]:
                    if result['embedding_type'] == 'CompanyName':
                        print(f"  ✅ {result['full_name']}: {result['document_preview']}")
                    else:
                        print(f"  📝 {result['full_name']} ({result['embedding_type']}): {result['document_preview'][:50]}...")
            
        except Exception as e:
            print(f"Error: {e}")

async def test_skill_search_precision():
    """Test precision of skill-specific searches."""
    
    print("\n💻 Testing Skill Search Precision")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Test skill searches
    skill_queries = [
        "Find Python developers",
        "Find React experts",
        "Find machine learning specialists",
        "Find AWS certified professionals"
    ]
    
    for query in skill_queries:
        print(f"\nQuery: {query}")
        
        try:
            results = await search_engine.search(query, top_k=3)
            
            embedding_types = results.get('embedding_types_to_search', [])
            print(f"Embedding types: {embedding_types}")
            
            if "Skills" in embedding_types:
                print("✅ Correctly targeting Skills embeddings")
            
            if results.get('results'):
                print("Matches:")
                for result in results['results'][:2]:
                    if result['embedding_type'] in ['Skills', 'TechnologiesUsed']:
                        print(f"  ✅ {result['full_name']}: {result['document_preview']}")
                    else:
                        print(f"  📝 {result['full_name']} ({result['embedding_type']}): {result['document_preview'][:50]}...")
            
        except Exception as e:
            print(f"Error: {e}")

async def main():
    """Main test function."""
    
    print("🚀 Separate Embeddings Test Suite")
    print("=" * 80)
    
    # Test 1: Processing with separate embeddings
    await test_separate_embeddings_processing()
    
    # Test 2: Search accuracy improvement
    await test_search_accuracy_improvement()
    
    # Test 3: Company search precision
    await test_company_search_precision()
    
    # Test 4: Skill search precision
    await test_skill_search_precision()
    
    print("\n" + "=" * 80)
    print("✅ Separate Embeddings Tests Completed!")
    print("\nKey Improvements:")
    print("🔹 Each company stored as separate embedding")
    print("🔹 Each skill stored as separate embedding")
    print("🔹 Each institution stored as separate embedding")
    print("🔹 Each technology stored as separate embedding")
    print("🔹 Improved search precision and accuracy")
    print("🔹 Better matching for specific queries")

if __name__ == "__main__":
    asyncio.run(main())
