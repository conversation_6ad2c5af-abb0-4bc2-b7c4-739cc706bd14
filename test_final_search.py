"""
Final test of the corrected hybrid search system.
"""

import asyncio
from chromaMongoSearch import search_resumes

async def test_final_search():
    """Test the final corrected search system."""
    
    print("🎯 Final Hybrid Search Test")
    print("=" * 50)
    
    # Test the original problematic query
    query = "Find females with experience in teaching in hindi"
    
    print(f"Query: {query}")
    print()
    
    results = await search_resumes(query)
    
    print("📊 Results:")
    print(f"   Total Results: {results['total_results']}")
    print(f"   MongoDB IDs Found: {results['mongodb_ids_found']}")
    print(f"   ChromaDB Docs Found: {results['chromadb_docs_found']}")
    print()
    
    print("🏆 Top 4 Matches:")
    for result in results['results']:
        print(f"   {result['rank']}. {result['full_name']} ({result['embedding_type']})")
        print(f"      Similarity Score: {result['similarity_score']:.3f}")
        print(f"      Match Distance: {result['match_distance']:.3f}")
        print(f"      MongoDB ID: {result['mongodb_id']}")
        print(f"      Document Preview: {result['document_preview'][:100]}...")
        print()
    
    print("🔄 Search Process:")
    for stage, description in results['search_stages'].items():
        print(f"   {stage}: {description}")
    
    print("\n" + "="*50)
    print("✅ Search System Summary:")
    print("1. MongoDB searches NON-embedding fields (Gender, Email, etc.)")
    print("2. ChromaDB searches only the 13 embedding fields")
    print("3. Vector similarity determines the best matches")
    print("4. Returns top 4 results based on semantic similarity")
    print("\nThe system now correctly separates:")
    print("• MongoDB: Structural/metadata filtering")
    print("• ChromaDB: Semantic content matching")

async def test_different_queries():
    """Test various query types."""
    
    print("\n🧪 Testing Different Query Types")
    print("=" * 50)
    
    queries = [
        "Python developer",
        "Machine learning expert", 
        "Project manager with agile",
        "Data scientist PhD",
        "Frontend React developer"
    ]
    
    for query in queries:
        print(f"\nQuery: {query}")
        results = await search_resumes(query)
        print(f"Results: {results['total_results']} | MongoDB IDs: {results['mongodb_ids_found']} | ChromaDB Docs: {results['chromadb_docs_found']}")
        
        if results['results']:
            top_result = results['results'][0]
            print(f"Top Match: {top_result['full_name']} (Score: {top_result['similarity_score']:.3f})")

async def main():
    """Main test function."""
    
    print("🚀 Final Corrected Hybrid Search System")
    print("=" * 80)
    
    await test_final_search()
    await test_different_queries()
    
    print("\n" + "=" * 80)
    print("🎉 System is working as designed!")
    print("\nKey Features:")
    print("✅ MongoDB searches non-embedding fields only")
    print("✅ ChromaDB searches 13 embedding fields only") 
    print("✅ Pure vector similarity matching")
    print("✅ Returns best 4 matches")
    print("✅ No explicit filters - natural language processing")

if __name__ == "__main__":
    asyncio.run(main())
