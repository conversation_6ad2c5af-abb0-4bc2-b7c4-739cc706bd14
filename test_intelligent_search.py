"""
Test the intelligent embedding type selection and multiple targeted searches.
"""

import asyncio
from chromaMongoSearch import ChromaMongoSearchEngine

async def test_embedding_type_selection():
    """Test OpenAI's embedding type selection for different queries."""
    
    print("🧠 Testing Intelligent Embedding Type Selection")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Test different types of queries
    test_queries = [
        "Find Dishita",  # Should select FullName
        "Find Python developers",  # Should select Skills, Role
        "Find people from IIT",  # Should select Institution
        "Find software engineers with React experience",  # Should select Role, Skills, TechnologiesUsed
        "Find project managers from Google",  # Should select Role, CompanyName
        "Find AWS certified professionals",  # Should select CertificationName, Skills
        "Find female teachers who teach hindi",  # Should select Role, Skills
        "Find data scientists with PhD",  # Should select Role, Institution
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 Test {i}: {query}")
        print("-" * 40)
        
        try:
            # Test embedding type selection
            embedding_types = await search_engine.determine_embedding_types_to_search(query)
            print(f"Selected embedding types: {embedding_types}")
            
            # Show reasoning
            if "FullName" in embedding_types:
                print("   🎯 FullName: Looking for specific person names")
            if "Skills" in embedding_types:
                print("   🎯 Skills: Looking for technical/soft skills")
            if "Role" in embedding_types:
                print("   🎯 Role: Looking for job titles/positions")
            if "Institution" in embedding_types:
                print("   🎯 Institution: Looking for educational background")
            if "CompanyName" in embedding_types:
                print("   🎯 CompanyName: Looking for company experience")
            if "TechnologiesUsed" in embedding_types:
                print("   🎯 TechnologiesUsed: Looking for project technologies")
            if "CertificationName" in embedding_types:
                print("   🎯 CertificationName: Looking for specific certifications")
                
        except Exception as e:
            print(f"❌ Error: {e}")

async def test_multiple_targeted_searches():
    """Test multiple targeted searches with different queries."""
    
    print("\n🎯 Testing Multiple Targeted Searches")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    test_queries = [
        "Find Dishita",  # Name-based search
        "Find Python developers",  # Skill-based search
        "Find female teachers who teach hindi",  # Multi-criteria search
        "Find people from IIT Mumbai",  # Institution-based search
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 Query {i}: {query}")
        print("-" * 50)
        
        try:
            results = await search_engine.search(query, top_k=4)
            
            print(f"📊 Results Summary:")
            print(f"   Embedding types searched: {results.get('embedding_types_to_search', [])}")
            print(f"   MongoDB IDs found: {results.get('mongodb_ids_found', 0)}")
            print(f"   ChromaDB docs found: {results.get('chromadb_docs_found', 0)}")
            print(f"   Total results: {results.get('total_results', 0)}")
            
            if results.get('results'):
                print(f"\n🏆 Top Results:")
                for result in results['results']:
                    print(f"   {result['rank']}. {result['full_name']} ({result['embedding_type']})")
                    print(f"      Similarity: {result['similarity_score']:.3f}")
                    print(f"      Preview: {result['document_preview'][:80]}...")
                    print()
            
            if results.get('search_stages'):
                print(f"🔄 Search Process:")
                for stage, description in results['search_stages'].items():
                    print(f"   {stage}: {description}")
                    
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()

async def test_name_search_precision():
    """Test precision of name-based searches."""
    
    print("\n👤 Testing Name Search Precision")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Test specific name searches
    name_queries = [
        "Find Dishita",
        "Find Sharma",
        "Find Dr. Aarti Verma",
        "Find people named Akash"
    ]
    
    for query in name_queries:
        print(f"\nQuery: {query}")
        
        try:
            results = await search_engine.search(query, top_k=3)
            
            embedding_types = results.get('embedding_types_to_search', [])
            print(f"Embedding types: {embedding_types}")
            
            if "FullName" in embedding_types:
                print("✅ Correctly identified as name search")
            else:
                print("⚠️  Did not identify as name search")
            
            if results.get('results'):
                print("Top matches:")
                for result in results['results'][:2]:
                    print(f"  - {result['full_name']} (Type: {result['embedding_type']}, Score: {result['similarity_score']:.3f})")
            else:
                print("No results found")
                
        except Exception as e:
            print(f"Error: {e}")

async def test_skill_search_precision():
    """Test precision of skill-based searches."""
    
    print("\n💻 Testing Skill Search Precision")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Test skill-based searches
    skill_queries = [
        "Find Python developers",
        "Find machine learning experts",
        "Find React developers",
        "Find people with communication skills"
    ]
    
    for query in skill_queries:
        print(f"\nQuery: {query}")
        
        try:
            results = await search_engine.search(query, top_k=3)
            
            embedding_types = results.get('embedding_types_to_search', [])
            print(f"Embedding types: {embedding_types}")
            
            if "Skills" in embedding_types:
                print("✅ Correctly identified skills component")
            if "Role" in embedding_types:
                print("✅ Correctly identified role component")
            
            if results.get('results'):
                print("Top matches:")
                for result in results['results'][:2]:
                    print(f"  - {result['full_name']} (Type: {result['embedding_type']}, Score: {result['similarity_score']:.3f})")
            else:
                print("No results found")
                
        except Exception as e:
            print(f"Error: {e}")

async def main():
    """Main test function."""
    
    print("🚀 Intelligent Search with Multiple Targeted Queries Test")
    print("=" * 80)
    
    # Test 1: Embedding type selection
    await test_embedding_type_selection()
    
    # Test 2: Multiple targeted searches
    await test_multiple_targeted_searches()
    
    # Test 3: Name search precision
    await test_name_search_precision()
    
    # Test 4: Skill search precision
    await test_skill_search_precision()
    
    print("\n" + "=" * 80)
    print("✅ Intelligent Search Tests Completed!")
    print("\nKey Features Tested:")
    print("🧠 OpenAI-powered embedding type selection")
    print("🎯 Multiple targeted searches per query")
    print("👤 Precise name-based searches")
    print("💻 Accurate skill-based searches")
    print("🔄 Smart deduplication across embedding types")
    print("📊 Comprehensive result analysis")

if __name__ == "__main__":
    asyncio.run(main())
