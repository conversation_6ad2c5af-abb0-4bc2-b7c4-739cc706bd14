"""
Simple test to verify the logger fix in DocumentProcessor.
"""

import asyncio
import os
from mongoDBInsertion import DocumentProcessor

def test_document_processor_initialization():
    """Test that DocumentProcessor initializes without logger errors."""
    
    print("🧪 Testing DocumentProcessor initialization...")
    
    try:
        # Test basic initialization
        processor = DocumentProcessor(
            database_name="test_db",
            collection_name="test_collection",
            enable_chromadb=False  # Disable ChromaDB for this test
        )
        
        print("✅ DocumentProcessor initialized successfully")
        print(f"   Logger: {processor.logger}")
        print(f"   Database: {processor.database_name}")
        print(f"   Collection: {processor.collection_name}")
        print(f"   ChromaDB enabled: {processor.enable_chromadb}")
        
        # Test logger functionality
        processor.logger.info("Test log message - this should work!")
        print("✅ Logger is working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error initializing DocumentProcessor: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chromadb_integration():
    """Test ChromaDB integration initialization."""
    
    print("\n🧪 Testing ChromaDB integration...")
    
    try:
        # Test with ChromaDB enabled (might fail if ChromaDB server not running)
        processor = DocumentProcessor(
            database_name="test_db",
            collection_name="test_collection",
            enable_chromadb=True,
            chroma_host="localhost",
            chroma_port=8000
        )
        
        print("✅ DocumentProcessor with ChromaDB initialized successfully")
        print(f"   ChromaDB enabled: {processor.enable_chromadb}")
        print(f"   ChromaDB processor: {processor.chromadb_processor}")
        
        if processor.chromadb_processor:
            print("✅ ChromaDB processor is available")
        else:
            print("⚠️  ChromaDB processor is None (server might not be running)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error with ChromaDB integration: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_process_single_file_method():
    """Test that process_single_file method can be called without logger errors."""
    
    print("\n🧪 Testing process_single_file method...")
    
    try:
        processor = DocumentProcessor(
            enable_chromadb=False  # Disable for testing
        )
        
        # Create a dummy file for testing
        test_file = "test_dummy.txt"
        with open(test_file, 'w') as f:
            f.write("This is a test file for logger testing.")
        
        temp_dir = "./temp_test"
        os.makedirs(temp_dir, exist_ok=True)
        
        # This should not crash due to logger issues
        # (it might fail for other reasons like file format, but logger should work)
        try:
            result = await processor.process_single_file(test_file, temp_dir)
            print(f"✅ process_single_file method executed (result: {result})")
        except Exception as e:
            # Expected to fail due to unsupported file format, but logger should work
            print(f"⚠️  process_single_file failed as expected: {e}")
            print("✅ But logger worked correctly during the process")
        
        # Cleanup
        if os.path.exists(test_file):
            os.remove(test_file)
        if os.path.exists(temp_dir):
            import shutil
            shutil.rmtree(temp_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error in process_single_file test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    
    print("🔬 Testing Logger Fix in DocumentProcessor")
    print("=" * 60)
    
    tests = [
        ("Basic Initialization", test_document_processor_initialization),
        ("ChromaDB Integration", test_chromadb_integration),
    ]
    
    results = []
    
    # Run synchronous tests
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    # Run async test
    print(f"\n📋 Running: Process Single File Method")
    print("-" * 40)
    async_result = asyncio.run(test_process_single_file_method())
    results.append(("Process Single File Method", async_result))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🏁 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Logger fix is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
