# MongoDB + ChromaDB Integration

This document explains the new integrated workflow where documents are processed and stored in MongoDB, and embeddings are automatically stored in ChromaDB with MongoDB IDs included in the metadata.

## Overview

The new integrated system provides a seamless workflow:

1. **Document Processing**: Documents are processed using `mongoDBInsertion.py`
2. **MongoDB Storage**: Structured data is stored in MongoDB and returns a MongoDB ObjectId
3. **Enhanced Data**: The MongoDB ID is added to the structured_data
4. **ChromaDB Storage**: The enhanced structured_data is sent to ChromaDB with MongoDB ID in metadata
5. **Unified System**: Both systems now have consistent data with cross-references

## Key Components

### 1. `mongoDBInsertion.py` (Enhanced)
- **Main processing engine** that handles document conversion, text extraction, and AI processing
- **MongoDB Integration**: Stores structured data and returns MongoDB ObjectId
- **ChromaDB Integration**: Automatically processes data with ChromaDB after MongoDB insertion
- **Thread-safe**: Supports parallel processing with proper locking

### 2. `chromdb_processor.py` (New)
- **ChromaDB operations** with MongoDB ID support
- **Direct data processing**: Accepts structured_data directly (no file I/O)
- **Metadata enhancement**: Includes MongoDB IDs in ChromaDB metadata
- **Cleanup support**: Can delete ChromaDB entries by MongoDB ID

### 3. `chromDB.py` (Deprecated)
- **Legacy standalone script** that reads JSON files from folders
- **Deprecated**: Use the new integrated approach instead
- **Migration path**: See examples for new workflow

## Usage Examples

### Basic Document Processing

```python
import asyncio
from mongoDBInsertion import process_documents_from_folder

async def process_documents():
    results = await process_documents_from_folder(
        folder_path="./documents",
        database_name="dbProductionV2",
        collection_name="collectionResumeV2",
        n_workers=4,
        enable_chromadb=True,  # Enable ChromaDB integration
        chroma_host="localhost",
        chroma_port=8000
    )
    print(f"Processed {results['successful']} documents successfully")

asyncio.run(process_documents())
```

### Single File Processing

```python
import asyncio
from mongoDBInsertion import DocumentProcessor

async def process_single_file():
    processor = DocumentProcessor(
        database_name="dbProductionV2",
        collection_name="collectionResumeV2",
        enable_chromadb=True
    )
    
    success = await processor.process_single_file(
        file_path="./resume.pdf",
        temp_dir="./temp"
    )
    
    if success:
        print("Document processed successfully!")
```

### Querying ChromaDB with MongoDB IDs

```python
from chromdb_processor import ChromaDBProcessor

# Initialize processor
chromadb_processor = ChromaDBProcessor()

# Find embeddings by MongoDB ID
results = chromadb_processor.collection.get(
    where={"mongodb_id": "507f1f77bcf86cd799439011"},
    include=["metadatas", "documents"]
)

print(f"Found {len(results['ids'])} embeddings for this MongoDB ID")
```

### Deleting Documents from Both Systems

```python
from mongoDBInsertion import DocumentProcessor
from bson import ObjectId

processor = DocumentProcessor(enable_chromadb=True)
mongodb_id = ObjectId("507f1f77bcf86cd799439011")

success = processor.delete_document_by_id(mongodb_id)
if success:
    print("Document deleted from both MongoDB and ChromaDB")
```

## Data Flow

```
Document File
     ↓
[mongoDBInsertion.py]
     ↓
Text Extraction (AWS Textract)
     ↓
AI Processing (OpenAI)
     ↓
structured_data
     ↓
MongoDB Storage → Returns ObjectId
     ↓
structured_data + mongodb_id
     ↓
[chromdb_processor.py]
     ↓
ChromaDB Storage (with MongoDB ID in metadata)
```

## Benefits

### ✅ **Integrated Workflow**
- Single process handles both MongoDB and ChromaDB
- No manual file management required
- Automatic data consistency

### ✅ **MongoDB ID Integration**
- ChromaDB metadata includes MongoDB ObjectIds
- Easy cross-referencing between systems
- Unified document management

### ✅ **Direct Data Processing**
- No intermediate JSON files needed
- structured_data flows directly to ChromaDB
- Reduced I/O operations

### ✅ **Better Error Handling**
- Comprehensive logging
- Thread-safe operations
- Graceful failure handling

### ✅ **Scalability**
- Parallel processing support
- Configurable worker counts
- Efficient resource utilization

## Configuration

### MongoDB Configuration
```python
database_name = "dbProductionV2"
collection_name = "collectionResumeV2"
```

### ChromaDB Configuration
```python
chroma_host = "localhost"
chroma_port = 8000
collection_name = "resumes_by_type"
```

### Processing Configuration
```python
n_workers = 4  # Number of parallel workers
enable_chromadb = True  # Enable/disable ChromaDB integration
```

## Migration from Old System

If you're currently using the standalone `chromDB.py`:

1. **Stop using** `chromDB.py` for new documents
2. **Use** `mongoDBInsertion.py` with `enable_chromadb=True`
3. **Update queries** to use MongoDB IDs in ChromaDB metadata
4. **Test** with `test_integration.py` and `example_usage.py`

## Testing

Run the integration tests:

```bash
python test_integration.py
```

See usage examples:

```bash
python example_usage.py
```

## Troubleshooting

### ChromaDB Connection Issues
- Ensure ChromaDB server is running on specified host:port
- Check firewall settings
- Verify ChromaDB server logs

### MongoDB Connection Issues
- Check MongoDB connection string in `helperMongoDb.py`
- Verify database and collection names
- Ensure MongoDB server is accessible

### Processing Failures
- Check file permissions
- Verify supported file formats
- Review logs for specific error messages
- Ensure sufficient disk space for temporary files

## File Structure

```
├── mongoDBInsertion.py      # Main processing engine (enhanced)
├── chromdb_processor.py     # ChromaDB operations (new)
├── chromDB.py              # Legacy script (deprecated)
├── test_integration.py     # Integration tests
├── example_usage.py        # Usage examples
└── README_MongoDB_ChromaDB_Integration.md  # This file
```

## Support

For issues or questions:
1. Check the logs for detailed error messages
2. Review the example files for proper usage
3. Test with the integration test script
4. Ensure all dependencies are properly installed
