"""
Test the deduplication fix to ensure each MongoDB ID appears only once in results.
"""

import asyncio
from chromaMongoSearch import ChromaMongoSearchEngine

async def test_deduplication():
    """Test that results are deduplicated by MongoDB ID."""
    
    print("🔍 Testing Result Deduplication")
    print("=" * 50)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Test query that might return multiple embedding types for same person
    query = "Find me a female teacher who teaches hindi"
    
    print(f"Query: {query}")
    print()
    
    results = await search_engine.search(query, top_k=10)  # Request more results to test deduplication
    
    print("📊 Results Analysis:")
    print(f"   Total Results: {results['total_results']}")
    print(f"   MongoDB IDs Found: {results['mongodb_ids_found']}")
    print(f"   ChromaDB Docs Found: {results['chromadb_docs_found']}")
    print()
    
    if results['results']:
        # Check for duplicate MongoDB IDs
        mongodb_ids = [result['mongodb_id'] for result in results['results']]
        unique_mongodb_ids = set(mongodb_ids)
        
        print(f"🔍 Deduplication Check:")
        print(f"   Total results returned: {len(results['results'])}")
        print(f"   Unique MongoDB IDs: {len(unique_mongodb_ids)}")
        print(f"   Duplicates removed: {len(mongodb_ids) - len(unique_mongodb_ids)}")
        
        if len(mongodb_ids) == len(unique_mongodb_ids):
            print("   ✅ No duplicates found - deduplication working!")
        else:
            print("   ❌ Duplicates still present!")
            
            # Show duplicates
            from collections import Counter
            id_counts = Counter(mongodb_ids)
            duplicates = {id_: count for id_, count in id_counts.items() if count > 1}
            print(f"   Duplicate IDs: {duplicates}")
        
        print(f"\n🏆 Top Results (Deduplicated):")
        for result in results['results']:
            print(f"   {result['rank']}. {result['full_name']} ({result['embedding_type']})")
            print(f"      Similarity: {result['similarity_score']:.3f}")
            print(f"      MongoDB ID: {result['mongodb_id']}")
            print(f"      Preview: {result['document_preview'][:80]}...")
            print()
    
    else:
        print("No results found.")

async def test_multiple_queries():
    """Test deduplication across different queries."""
    
    print("\n🧪 Testing Deduplication Across Multiple Queries")
    print("=" * 60)
    
    queries = [
        "Find females with teaching experience",
        "Looking for software engineers",
        "Need data scientists",
        "Search for project managers"
    ]
    
    search_engine = ChromaMongoSearchEngine()
    
    for i, query in enumerate(queries, 1):
        print(f"\n📋 Query {i}: {query}")
        print("-" * 40)
        
        try:
            results = await search_engine.search(query, top_k=6)
            
            if results['results']:
                mongodb_ids = [result['mongodb_id'] for result in results['results']]
                unique_ids = set(mongodb_ids)
                
                print(f"Results: {len(results['results'])}")
                print(f"Unique MongoDB IDs: {len(unique_ids)}")
                print(f"Deduplication: {'✅ Working' if len(mongodb_ids) == len(unique_ids) else '❌ Failed'}")
                
                # Show top 2 results
                for result in results['results'][:2]:
                    print(f"  - {result['full_name']} (Score: {result['similarity_score']:.3f}, Type: {result['embedding_type']})")
            else:
                print("No results found")
                
        except Exception as e:
            print(f"Error: {e}")

async def test_before_after_comparison():
    """Show what results would look like before and after deduplication."""
    
    print("\n📊 Before/After Deduplication Comparison")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    query = "Find females with teaching experience"
    
    print(f"Query: {query}")
    print()
    
    # Get ChromaDB results to simulate before deduplication
    print("Step 1: Get MongoDB IDs")
    mongodb_query = await search_engine.generate_mongodb_search_query(query)
    mongodb_ids = await search_engine.search_mongodb_with_generated_query(mongodb_query, limit=50)
    print(f"   MongoDB IDs: {len(mongodb_ids)}")
    
    print("\nStep 2: Get ChromaDB documents")
    chromadb_results = search_engine.get_chromadb_docs_by_mongodb_ids(mongodb_ids)
    print(f"   ChromaDB docs: {len(chromadb_results.get('ids', []))}")
    
    if chromadb_results.get('metadatas'):
        # Analyze what we would get before deduplication
        mongodb_id_counts = {}
        for metadata in chromadb_results['metadatas']:
            mongodb_id = metadata.get('mongodb_id')
            if mongodb_id:
                mongodb_id_counts[mongodb_id] = mongodb_id_counts.get(mongodb_id, 0) + 1
        
        print(f"\nStep 3: Analysis before deduplication")
        print(f"   Unique people: {len(mongodb_id_counts)}")
        print(f"   Total embedding entries: {sum(mongodb_id_counts.values())}")
        
        # Show people with multiple embeddings
        multiple_embeddings = {id_: count for id_, count in mongodb_id_counts.items() if count > 1}
        if multiple_embeddings:
            print(f"   People with multiple embeddings: {len(multiple_embeddings)}")
            for mongodb_id, count in list(multiple_embeddings.items())[:3]:
                print(f"     MongoDB ID {mongodb_id}: {count} embeddings")
    
    print(f"\nStep 4: Final results (after deduplication)")
    results = await search_engine.search(query, top_k=4)
    print(f"   Final unique results: {results['total_results']}")
    
    if results['results']:
        for result in results['results']:
            print(f"   {result['rank']}. {result['full_name']} (Best match: {result['embedding_type']})")

async def main():
    """Main test function."""
    
    print("🚀 Result Deduplication Test Suite")
    print("=" * 80)
    
    # Test 1: Basic deduplication
    await test_deduplication()
    
    # Test 2: Multiple queries
    await test_multiple_queries()
    
    # Test 3: Before/after comparison
    await test_before_after_comparison()
    
    print("\n" + "=" * 80)
    print("✅ Deduplication Tests Completed!")
    print("\nDeduplication Logic:")
    print("✅ Groups results by MongoDB ID")
    print("✅ Keeps only the best match per person (highest similarity)")
    print("✅ Sorts final results by similarity score")
    print("✅ Ensures each person appears only once")
    print("✅ Shows the most relevant embedding type for each person")

if __name__ == "__main__":
    asyncio.run(main())
