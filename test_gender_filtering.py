"""
Test script to verify gender filtering in the hybrid search system.
"""

import asyncio
from chromaMongoSearch import ChromaMongoSearchEngine
from helperMongoDb import MongoDBClient

async def test_gender_filtering():
    """Test gender filtering functionality."""
    
    print("🔍 Testing Gender Filtering in Hybrid Search")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Test queries with gender specifications
    test_queries = [
        "Find females with experience in teaching in hindi",
        "Looking for male software engineers with Python",
        "Need women data scientists with machine learning",
        "Search for men with project management experience"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 Test {i}: {query}")
        print("-" * 50)
        
        try:
            # Test the filter extraction first
            filters = search_engine.extract_filters_from_query(query)
            print(f"Extracted filters: {filters}")
            
            # Perform the search
            results = await search_engine.search(query, top_k=5)
            
            print(f"MongoDB IDs found: {results.get('mongodb_ids_found', 0)}")
            print(f"ChromaDB docs found: {results.get('chromadb_docs_found', 0)}")
            print(f"Total results: {results.get('total_results', 0)}")
            
            if results.get('results'):
                print("\nTop results:")
                for result in results['results']:
                    print(f"  {result['rank']}. {result['full_name']} ({result['embedding_type']})")
                    print(f"     Similarity: {result['similarity_score']:.3f}")
                    print(f"     MongoDB ID: {result['mongodb_id']}")
                    
                    # Check actual gender from MongoDB
                    gender = await get_gender_from_mongodb(result['mongodb_id'])
                    print(f"     Actual Gender: {gender}")
                    print()
            else:
                print("No results found")
                
        except Exception as e:
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()

async def get_gender_from_mongodb(mongodb_id: str) -> str:
    """Get gender information from MongoDB for verification."""
    
    try:
        mongo_client = MongoDBClient(db_name="dbProductionV2")
        collection = mongo_client.db["collectionResumeV2"]
        
        from bson import ObjectId
        doc = collection.find_one(
            {"_id": ObjectId(mongodb_id)},
            {"Resume.PersonalInformation.Gender": 1}
        )
        
        if doc and "Resume" in doc:
            gender = doc["Resume"]["PersonalInformation"].get("Gender", "Not specified")
            return gender
        else:
            return "Not found"
            
    except Exception as e:
        return f"Error: {e}"

async def test_filter_extraction():
    """Test the filter extraction logic."""
    
    print("\n🧪 Testing Filter Extraction Logic")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    test_cases = [
        "Find females with teaching experience",
        "Looking for male engineers",
        "Need women with leadership skills",
        "Search for men in management",
        "Find senior developers",
        "Looking for junior analysts",
        "Need PhD candidates",
        "Search for masters degree holders",
        "Find bachelor graduates",
        "Looking for experienced professionals"
    ]
    
    for query in test_cases:
        filters = search_engine.extract_filters_from_query(query)
        print(f"Query: {query}")
        print(f"Filters: {filters}")
        print()

async def test_mongodb_direct_search():
    """Test MongoDB search directly to see available data."""
    
    print("\n📊 Testing MongoDB Direct Search")
    print("=" * 60)
    
    try:
        mongo_client = MongoDBClient(db_name="dbProductionV2")
        collection = mongo_client.db["collectionResumeV2"]
        
        # Count total documents
        total_count = collection.count_documents({})
        print(f"Total documents in MongoDB: {total_count}")
        
        # Count by gender
        female_count = collection.count_documents({
            "Resume.PersonalInformation.Gender": {"$regex": "female", "$options": "i"}
        })
        male_count = collection.count_documents({
            "Resume.PersonalInformation.Gender": {"$regex": "male", "$options": "i"}
        })
        
        print(f"Female documents: {female_count}")
        print(f"Male documents: {male_count}")
        print(f"Other/Unspecified: {total_count - female_count - male_count}")
        
        # Sample female documents
        print("\nSample female documents:")
        female_docs = collection.find(
            {"Resume.PersonalInformation.Gender": {"$regex": "female", "$options": "i"}},
            {"Resume.PersonalInformation.FullName": 1, "Resume.PersonalInformation.Gender": 1}
        ).limit(5)
        
        for doc in female_docs:
            name = doc["Resume"]["PersonalInformation"].get("FullName", "Unknown")
            gender = doc["Resume"]["PersonalInformation"].get("Gender", "Unknown")
            print(f"  - {name} ({gender})")
        
        # Sample male documents
        print("\nSample male documents:")
        male_docs = collection.find(
            {"Resume.PersonalInformation.Gender": {"$regex": "male", "$options": "i"}},
            {"Resume.PersonalInformation.FullName": 1, "Resume.PersonalInformation.Gender": 1}
        ).limit(5)
        
        for doc in male_docs:
            name = doc["Resume"]["PersonalInformation"].get("FullName", "Unknown")
            gender = doc["Resume"]["PersonalInformation"].get("Gender", "Unknown")
            print(f"  - {name} ({gender})")
            
    except Exception as e:
        print(f"Error accessing MongoDB: {e}")
        import traceback
        traceback.print_exc()

async def test_specific_female_search():
    """Test specific search for females with teaching experience."""
    
    print("\n🎯 Testing Specific Female Teaching Search")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    query = "Find females with experience in teaching in hindi"
    print(f"Query: {query}")
    
    # Step by step testing
    print("\nStep 1: Extract filters")
    filters = search_engine.extract_filters_from_query(query)
    print(f"Filters: {filters}")
    
    print("\nStep 2: MongoDB search with filters")
    mongodb_ids = search_engine.search_mongodb_excluding_embedding_fields(query, limit=100)
    print(f"MongoDB IDs found: {len(mongodb_ids)}")
    
    if mongodb_ids:
        print("Sample MongoDB IDs:", mongodb_ids[:3])
        
        # Verify gender for each ID
        print("\nVerifying gender for found IDs:")
        for i, mongodb_id in enumerate(mongodb_ids[:5]):
            gender = await get_gender_from_mongodb(mongodb_id)
            print(f"  {i+1}. ID: {mongodb_id} - Gender: {gender}")
    
    print("\nStep 3: Full search")
    results = await search_engine.search(query, top_k=5)
    
    print(f"Final results: {results.get('total_results', 0)}")
    if results.get('results'):
        for result in results['results']:
            gender = await get_gender_from_mongodb(result['mongodb_id'])
            print(f"  {result['full_name']} - Gender: {gender} - Similarity: {result['similarity_score']:.3f}")

async def main():
    """Main test function."""
    
    print("🔬 Gender Filtering Test Suite")
    print("=" * 80)
    
    # Test 1: Filter extraction
    await test_filter_extraction()
    
    # Test 2: MongoDB direct search
    await test_mongodb_direct_search()
    
    # Test 3: Specific female search
    await test_specific_female_search()
    
    # Test 4: Gender filtering in search
    await test_gender_filtering()
    
    print("\n" + "=" * 80)
    print("🏁 Gender Filtering Tests Completed!")

if __name__ == "__main__":
    asyncio.run(main())
