"""
MongoDB Document Insertion Tool

This module processes various document formats (PDF, DOC, DOCX, images),
converts them to PDF, extracts text using AWS Textract and OpenAI,
and stores the results in MongoDB with GridFS for file storage.

Required additional dependencies (install via pip):
- PyMuPDF (fitz): pip install PyMuPDF
- python-docx: pip install python-docx
- pywin32: pip install pywin32 (Windows only, for DOC conversion)
- Pillow: pip install Pillow (usually already installed)

Usage:
    # Process all files in a folder
    python mongoDBInsertion.py /path/to/documents

    # Process with custom database settings
    python mongoDBInsertion.py /path/to/documents --database my-db --collection my-collection

    # List processed documents
    python mongoDBInsertion.py --list
"""

import os
import json
import asyncio
import hashlib
import logging
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
import shutil
import tempfile
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from queue import Queue

# Document conversion libraries
try:
    from PIL import Image
except ImportError:
    print("Warning: PIL (Pillow) not found. Image conversion will not work.")
    Image = None

try:
    import fitz  # PyMuPDF for PDF operations
except ImportError:
    print("Warning: PyMuPDF (fitz) not found. Advanced PDF operations may not work.")
    fitz = None

try:
    from docx import Document
except ImportError:
    print("Warning: python-docx not found. DOCX conversion will not work.")
    Document = None

try:
    import win32com.client  # For .doc files on Windows
except ImportError:
    print("Warning: pywin32 not found. DOC conversion on Windows will not work.")
    win32com = None

# Existing modules from the project
from openai import OpenAI
from utils import MloadConfig
from AWS_Async import extractByAwsTextract
from helperMongoDb import MongoDBClient
import gridfs
from bson import ObjectId
from chromdb_processor import ChromaDBProcessor

# Constants
SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
SUPPORTED_DOC_FORMATS = ['.doc', '.docx']
PDF_FORMAT = '.pdf'
TEXT_CONVERSION_SUFFIX = "_Combined"
GPT_RESPONSE_SUFFIX = "_GPT_Response"

class DocumentProcessor:
    """
    A comprehensive document processor that handles various file formats,
    converts them to PDF, extracts text using AWS Textract and OpenAI,
    and stores the results in MongoDB.
    """
    
    def __init__(self,
                 database_name: str = "dbProductionV2",
                 collection_name: str = "collectionResumeV2",
                 vendor_name: str = "Resume_Schema",
                 n_workers: int = 12,
                 enable_chromadb: bool = True,
                 chroma_host: str = "localhost",
                 chroma_port: int = 8000):
        """
        Initialize the DocumentProcessor.

        Args:
            database_name: MongoDB database name
            collection_name: MongoDB collection name
            vendor_name: Vendor name for processing configuration
            n_workers: Number of parallel workers for processing
            enable_chromadb: Whether to enable ChromaDB integration
            chroma_host: ChromaDB server host
            chroma_port: ChromaDB server port
        """
        self.database_name = database_name
        self.collection_name = collection_name
        self.vendor_name = vendor_name
        self.n_workers = n_workers
        self.enable_chromadb = enable_chromadb

        # Load configuration
        self.config = MloadConfig()

        # Initialize OpenAI client
        self.openai_client = OpenAI(
            api_key="************************************************************************************",
            base_url="https://api.x.ai/v1",
        )

        # Initialize ChromaDB processor if enabled
        if self.enable_chromadb:
            try:
                self.chromadb_processor = ChromaDBProcessor(
                    chroma_host=chroma_host,
                    chroma_port=chroma_port,
                    # openai_api_key="************************************************************************************",
                    # openai_base_url="https://api.x.ai/v1"
                )
                self.logger.info("ChromaDB processor initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize ChromaDB processor: {e}")
                self.enable_chromadb = False
                self.chromadb_processor = None
        else:
            self.chromadb_processor = None

        # Initialize MongoDB client with custom database
        self.mongo_client = MongoDBClient(db_name=database_name)

        # Initialize GridFS for file storage
        self.fs = gridfs.GridFS(self.mongo_client.db)

        # Setup logging
        self.setup_logging()

        # Load system prompt and response format
        self.load_ai_configurations()

        # Thread-safe locks for shared resources
        self._mongo_lock = threading.Lock()
        self._gridfs_lock = threading.Lock()
        self._openai_lock = threading.Lock()  # Rate limiting for OpenAI API
        self._com_lock = threading.Lock()  # For Windows COM operations (Word automation)
    
    def setup_logging(self):
        """Setup logging configuration."""
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(log_dir, 'document_processor.log')),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_ai_configurations(self):
        """Load system prompt and response format for AI processing."""
        try:
            # Load system prompt
            system_prompt_path = self.config.get("systemPromptFilePath")
            with open(system_prompt_path, 'r', encoding='utf-8') as file:
                self.system_prompt = file.read()
            
            # Load response format
            response_format_path = self.config.get("responseFormatFilePath")
            with open(response_format_path, 'r', encoding='utf-8') as file:
                response_formats = json.load(file)
                self.response_format = response_formats.get(self.vendor_name, {})
            
            self.logger.info("AI configurations loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Error loading AI configurations: {e}")
            raise
    
    def calculate_checksum(self, file_path: str) -> str:
        """Calculate SHA-256 checksum of a file."""
        sha256_hash = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except Exception as e:
            self.logger.error(f"Error calculating checksum for {file_path}: {e}")
            raise
    
    def is_duplicate(self, checksum: str) -> bool:
        """Check if a document with the same checksum already exists."""
        try:
            collection = self.mongo_client.db[self.collection_name]
            existing = collection.find_one({"checksum": checksum})
            return existing is not None
        except Exception as e:
            self.logger.error(f"Error checking for duplicates: {e}")
            return False
    
    def convert_image_to_pdf(self, image_path: str, output_path: str) -> bool:
        """Convert image file to PDF."""
        if Image is None:
            self.logger.error("PIL (Pillow) not available. Cannot convert images to PDF.")
            return False

        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # Save as PDF
                img.save(output_path, "PDF", resolution=100.0)
                self.logger.info(f"Converted image {image_path} to PDF: {output_path}")
                return True

        except Exception as e:
            self.logger.error(f"Error converting image {image_path} to PDF: {e}")
            return False

    def convert_docx_to_pdf(self, docx_path: str, output_path: str) -> bool:
        """Convert DOCX file to PDF using COM automation on Windows."""
        try:
            # For Windows, use COM automation
            if os.name == 'nt':
                return self.convert_doc_to_pdf_windows(docx_path, output_path)
            else:
                # For other platforms, you might need LibreOffice or other tools
                self.logger.warning("DOCX to PDF conversion not fully supported on non-Windows platforms")
                return False

        except Exception as e:
            self.logger.error(f"Error converting DOCX {docx_path} to PDF: {e}")
            return False

    def convert_doc_to_pdf_windows(self, doc_path: str, output_path: str) -> bool:
        """Convert DOC/DOCX to PDF using Windows COM automation (thread-safe)."""
        if win32com is None:
            self.logger.error("pywin32 not available. Cannot convert DOC/DOCX files on Windows.")
            return False

        # Use lock to prevent concurrent COM operations
        with self._com_lock:
            try:
                import pythoncom

                # Initialize COM for this thread
                pythoncom.CoInitialize()

                try:
                    # Use Microsoft Word COM automation
                    word = win32com.client.Dispatch("Word.Application")
                    word.Visible = False

                    # Open document
                    doc = word.Documents.Open(os.path.abspath(doc_path))

                    # Save as PDF (format 17 is PDF)
                    doc.SaveAs(os.path.abspath(output_path), FileFormat=17)

                    # Close document and Word
                    doc.Close()
                    word.Quit()

                    self.logger.info(f"Converted {doc_path} to PDF: {output_path}")
                    return True

                finally:
                    # Always uninitialize COM
                    pythoncom.CoUninitialize()

            except Exception as e:
                self.logger.error(f"Error converting {doc_path} to PDF using COM: {e}")
                return False
    
    def convert_to_pdf(self, file_path: str, output_dir: str) -> Optional[str]:
        """
        Convert various file formats to PDF.
        
        Args:
            file_path: Path to the input file
            output_dir: Directory to save the converted PDF
            
        Returns:
            Path to the converted PDF file, or None if conversion failed
        """
        file_ext = Path(file_path).suffix.lower()
        file_name = Path(file_path).stem
        output_path = os.path.join(output_dir, f"{file_name}.pdf")
        
        # If already PDF, just copy it
        if file_ext == PDF_FORMAT:
            shutil.copy2(file_path, output_path)
            return output_path
        
        # Convert based on file type
        if file_ext in SUPPORTED_IMAGE_FORMATS:
            if self.convert_image_to_pdf(file_path, output_path):
                return output_path
        elif file_ext in SUPPORTED_DOC_FORMATS:
            if self.convert_docx_to_pdf(file_path, output_path):
                return output_path
        else:
            self.logger.warning(f"Unsupported file format: {file_ext}")
            return None

        return None

    async def extract_text_from_pdf(self, pdf_path: str) -> Optional[str]:
        """Extract text from PDF using AWS Textract."""
        try:
            # Create folder structure similar to existing system
            pdf_name = Path(pdf_path).stem
            pdf_folder = os.path.join(os.path.dirname(pdf_path), pdf_name)
            os.makedirs(pdf_folder, exist_ok=True)

            # Multiple possible text file paths to check (based on AWS Textract output)
            possible_text_files = [
                os.path.join(pdf_folder, f"{pdf_name}_strUserPrompt.txt"),  # Primary file from AWS Textract
                os.path.join(pdf_folder, f"{pdf_name}_ExtractedText.txt"),  # Alternative file
                os.path.join(pdf_folder, f"{pdf_name}{TEXT_CONVERSION_SUFFIX}.txt"),  # Legacy format
                os.path.join(pdf_folder, f"{pdf_name}_Combined.txt")  # Another possible format
            ]

            # Extract text if not already exists
            text_file_exists = any(os.path.exists(path) for path in possible_text_files)
            if not text_file_exists:
                await extractByAwsTextract(pdf_path)

            # Try to read from any of the possible text files
            for text_file_path in possible_text_files:
                if os.path.exists(text_file_path):
                    try:
                        with open(text_file_path, 'r', encoding='utf-8') as file:
                            content = file.read().strip()
                            if content:  # Make sure we have actual content
                                self.logger.info(f"Successfully read text from: {text_file_path}")
                                return content
                    except Exception as e:
                        self.logger.warning(f"Failed to read {text_file_path}: {e}")
                        continue

            # If no text file found, check the folder contents for debugging
            if os.path.exists(pdf_folder):
                files_in_folder = os.listdir(pdf_folder)
                self.logger.error(f"Text extraction failed for {pdf_path}")
                self.logger.error(f"Files in extraction folder: {files_in_folder}")
            else:
                self.logger.error(f"Extraction folder not created: {pdf_folder}")

            return None

        except Exception as e:
            self.logger.error(f"Error extracting text from {pdf_path}: {e}")
            return None
    
    def process_with_openai(self, text_content: str) -> Optional[Dict[str, Any]]:
        """Process extracted text using OpenAI to structure the data (thread-safe)."""
        with self._openai_lock:  # Rate limiting for OpenAI API
            try:
                self.logger.info("Starting OpenAI processing...")

                response = self.openai_client.beta.chat.completions.parse(
                    model="grok-3-mini",
                    messages=[
                        {"role": "system", "content": self.system_prompt},
                        {"role": "user", "content": text_content}
                    ],
                    response_format=self.response_format,
                    reasoning_effort="high"
                    # temperature=0,
                    # max_completion_tokens=16384,
                    # seed=33
                )

                self.logger.info("OpenAI processing completed")

                # Parse the response
                structured_data = json.loads(response.choices[0].message.content)
                return structured_data

            except Exception as e:
                self.logger.error(f"Error processing with OpenAI: {e}")
                return None
    
    def store_pdf_in_gridfs(self, pdf_path: str, filename: str) -> Optional[ObjectId]:
        """Store PDF file in MongoDB GridFS (thread-safe)."""
        with self._gridfs_lock:
            try:
                with open(pdf_path, 'rb') as pdf_file:
                    file_id = self.fs.put(
                        pdf_file,
                        filename=filename,
                        content_type='application/pdf',
                        upload_date=datetime.now()
                    )
                    self.logger.info(f"Stored PDF {filename} in GridFS with ID: {file_id}")
                    return file_id

            except Exception as e:
                self.logger.error(f"Error storing PDF in GridFS: {e}")
                return None

    def insert_to_mongodb(self, structured_data: Dict[str, Any], checksum: str,
                         pdf_file_id: Optional[ObjectId], original_filename: str) -> Optional[ObjectId]:
        """Insert structured data and PDF reference to MongoDB (thread-safe)."""
        with self._mongo_lock:
            try:
                collection = self.mongo_client.db[self.collection_name]

                # Prepare the document
                document = {
                    **structured_data,
                    "checksum": checksum,
                    "timestamp": datetime.now(),
                    "original_filename": original_filename,
                    "pdf_file_id": pdf_file_id,
                    "vendor_name": self.vendor_name,
                    "processed_by": "mongoDBInsertion.py"
                }

                # Insert the document
                result = collection.insert_one(document)
                self.logger.info(f"Inserted document with ID: {result.inserted_id}")
                return result.inserted_id

            except Exception as e:
                self.logger.error(f"Error inserting to MongoDB: {e}")
                return None

    def is_duplicate_thread_safe(self, checksum: str) -> bool:
        """Check if a document with the same checksum already exists (thread-safe)."""
        with self._mongo_lock:
            try:
                collection = self.mongo_client.db[self.collection_name]
                existing = collection.find_one({"checksum": checksum})
                return existing is not None
            except Exception as e:
                self.logger.error(f"Error checking for duplicates: {e}")
                return False

    def delete_document_by_id(self, mongodb_id: ObjectId) -> bool:
        """
        Delete a document from both MongoDB and ChromaDB by MongoDB ID.

        Args:
            mongodb_id: MongoDB ObjectId to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            # Delete from MongoDB
            with self._mongo_lock:
                collection = self.mongo_client.db[self.collection_name]
                mongo_result = collection.delete_one({"_id": mongodb_id})

                if mongo_result.deleted_count > 0:
                    self.logger.info(f"Deleted document from MongoDB: {mongodb_id}")
                    mongo_success = True
                else:
                    self.logger.warning(f"No document found in MongoDB with ID: {mongodb_id}")
                    mongo_success = False

            # Delete from ChromaDB if enabled
            chromadb_success = True
            if self.enable_chromadb and self.chromadb_processor:
                try:
                    chromadb_success = self.chromadb_processor.delete_by_mongodb_id(mongodb_id)
                except Exception as e:
                    self.logger.error(f"Error deleting from ChromaDB: {e}")
                    chromadb_success = False

            return mongo_success and chromadb_success

        except Exception as e:
            self.logger.error(f"Error deleting document {mongodb_id}: {e}")
            return False

    async def process_single_file(self, file_path: str, temp_dir: str) -> bool:
        """
        Process a single file: convert to PDF, extract text, process with AI, and store in MongoDB.

        Args:
            file_path: Path to the file to process
            temp_dir: Temporary directory for intermediate files

        Returns:
            True if processing was successful, False otherwise
        """
        try:
            original_filename = os.path.basename(file_path)
            self.logger.info(f"Processing file: {original_filename}")

            # Calculate checksum of original file
            checksum = self.calculate_checksum(file_path)

            # Check for duplicates (thread-safe)
            if self.is_duplicate_thread_safe(checksum):
                self.logger.info(f"Duplicate file detected, skipping: {original_filename}")
                return True

            # Convert to PDF if necessary
            pdf_path = self.convert_to_pdf(file_path, temp_dir)
            if not pdf_path:
                self.logger.error(f"Failed to convert {original_filename} to PDF")
                return False

            # Extract text using AWS Textract
            extracted_text = await self.extract_text_from_pdf(pdf_path)
            if not extracted_text:
                self.logger.error(f"Failed to extract text from {original_filename}")
                return False

            # Process with OpenAI (thread-safe with rate limiting)
            structured_data = self.process_with_openai(extracted_text)
            if not structured_data:
                self.logger.error(f"Failed to process {original_filename} with OpenAI")
                return False

            # Store PDF in GridFS (thread-safe)
            pdf_file_id = self.store_pdf_in_gridfs(pdf_path, original_filename)

            # Insert to MongoDB (thread-safe) - now returns ObjectId
            mongodb_id = self.insert_to_mongodb(structured_data, checksum, pdf_file_id, original_filename)

            if mongodb_id:
                self.logger.info(f"Successfully stored in MongoDB with ID: {mongodb_id}")

                # Add MongoDB ID to structured_data for ChromaDB
                structured_data_with_id = {
                    **structured_data,
                    "mongodb_id": str(mongodb_id)
                }

                # Process with ChromaDB if enabled
                if self.enable_chromadb and self.chromadb_processor:
                    try:
                        chromadb_success = self.chromadb_processor.process_structured_data(
                            structured_data_with_id, mongodb_id, original_filename
                        )
                        if chromadb_success:
                            self.logger.info(f"Successfully stored embeddings in ChromaDB for: {original_filename}")
                        else:
                            self.logger.warning(f"Failed to store embeddings in ChromaDB for: {original_filename}")
                    except Exception as e:
                        self.logger.error(f"ChromaDB processing error for {original_filename}: {e}")

                self.logger.info(f"Successfully processed and stored: {original_filename}")
                return True
            else:
                self.logger.error(f"Failed to store {original_filename} in MongoDB")
                return False

        except Exception as e:
            self.logger.error(f"Error processing file {file_path}: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def process_single_file_sync(self, file_path: str, temp_dir: str) -> bool:
        """
        Synchronous wrapper for process_single_file to use with ThreadPoolExecutor.

        Args:
            file_path: Path to the file to process
            temp_dir: Temporary directory for intermediate files

        Returns:
            True if processing was successful, False otherwise
        """
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self.process_single_file(file_path, temp_dir))
        finally:
            loop.close()

    def get_processable_files(self, folder_path: str) -> List[str]:
        """
        Get list of processable files from the folder.

        Args:
            folder_path: Path to the folder containing files

        Returns:
            List of file paths that can be processed
        """
        processable_files = []
        supported_extensions = SUPPORTED_IMAGE_FORMATS + SUPPORTED_DOC_FORMATS + [PDF_FORMAT]

        try:
            if not os.path.exists(folder_path):
                self.logger.error(f"Folder does not exist: {folder_path}")
                return processable_files

            for file_name in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file_name)

                # Skip directories
                if os.path.isdir(file_path):
                    continue

                # Check if file extension is supported
                file_ext = Path(file_name).suffix.lower()
                if file_ext in supported_extensions:
                    processable_files.append(file_path)
                    self.logger.info(f"Found processable file: {file_name}")
                else:
                    self.logger.warning(f"Unsupported file format, skipping: {file_name}")

            self.logger.info(f"Found {len(processable_files)} processable files in {folder_path}")
            return processable_files

        except Exception as e:
            self.logger.error(f"Error scanning folder {folder_path}: {e}")
            return processable_files

    async def process_folder(self, folder_path: str) -> Dict[str, Any]:
        """
        Process all files in the specified folder using parallel processing.

        Args:
            folder_path: Path to the folder containing files to process

        Returns:
            Dictionary containing processing results and statistics
        """
        start_time = datetime.now()
        results = {
            "start_time": start_time,
            "folder_path": folder_path,
            "total_files": 0,
            "processed_successfully": 0,
            "failed_files": 0,
            "duplicate_files": 0,
            "processed_files": [],
            "failed_files_list": [],
            "duplicate_files_list": [],
            "n_workers": self.n_workers
        }

        try:
            self.logger.info(f"Starting parallel folder processing: {folder_path}")
            self.logger.info(f"Using {self.n_workers} parallel workers")

            # Get list of processable files
            files_to_process = self.get_processable_files(folder_path)
            results["total_files"] = len(files_to_process)

            if not files_to_process:
                self.logger.warning("No processable files found in the folder")
                return results

            # Pre-filter duplicates to avoid unnecessary processing
            self.logger.info("Pre-filtering duplicate files...")
            non_duplicate_files = []
            for file_path in files_to_process:
                file_name = os.path.basename(file_path)
                try:
                    checksum = self.calculate_checksum(file_path)
                    if self.is_duplicate_thread_safe(checksum):
                        results["duplicate_files"] += 1
                        results["duplicate_files_list"].append(file_name)
                        self.logger.info(f"Duplicate detected, skipping: {file_name}")
                    else:
                        non_duplicate_files.append(file_path)
                except Exception as e:
                    self.logger.error(f"Error checking duplicate for {file_name}: {e}")
                    results["failed_files"] += 1
                    results["failed_files_list"].append(file_name)

            self.logger.info(f"Found {len(non_duplicate_files)} non-duplicate files to process")

            if not non_duplicate_files:
                self.logger.info("No new files to process after duplicate filtering")
                return results

            # Create temporary directory for intermediate files
            with tempfile.TemporaryDirectory() as temp_dir:
                self.logger.info(f"Using temporary directory: {temp_dir}")

                # Process files in parallel using ThreadPoolExecutor
                with ThreadPoolExecutor(max_workers=self.n_workers) as executor:
                    self.logger.info(f"Starting parallel processing with {self.n_workers} workers...")

                    # Submit all tasks
                    future_to_file = {
                        executor.submit(self.process_single_file_sync, file_path, temp_dir): file_path
                        for file_path in non_duplicate_files
                    }

                    # Process completed tasks
                    completed = 0
                    for future in as_completed(future_to_file):
                        file_path = future_to_file[future]
                        file_name = os.path.basename(file_path)
                        completed += 1

                        try:
                            success = future.result()

                            if success:
                                results["processed_successfully"] += 1
                                results["processed_files"].append(file_name)
                                self.logger.info(f"[{completed}/{len(non_duplicate_files)}] ✓ {file_name}")
                            else:
                                results["failed_files"] += 1
                                results["failed_files_list"].append(file_name)
                                self.logger.error(f"[{completed}/{len(non_duplicate_files)}] ✗ {file_name}")

                        except Exception as e:
                            self.logger.error(f"[{completed}/{len(non_duplicate_files)}] Exception processing {file_name}: {e}")
                            results["failed_files"] += 1
                            results["failed_files_list"].append(file_name)

            # Calculate processing time
            end_time = datetime.now()
            results["end_time"] = end_time
            results["processing_time"] = str(end_time - start_time)

            # Log summary
            self.logger.info("=" * 60)
            self.logger.info("PARALLEL PROCESSING SUMMARY")
            self.logger.info("=" * 60)
            self.logger.info(f"Folder: {folder_path}")
            self.logger.info(f"Workers: {self.n_workers}")
            self.logger.info(f"Total files found: {results['total_files']}")
            self.logger.info(f"Successfully processed: {results['processed_successfully']}")
            self.logger.info(f"Failed: {results['failed_files']}")
            self.logger.info(f"Duplicates skipped: {results['duplicate_files']}")
            self.logger.info(f"Processing time: {results['processing_time']}")

            # Calculate throughput
            if results['processed_successfully'] > 0:
                total_seconds = (end_time - start_time).total_seconds()
                throughput = results['processed_successfully'] / total_seconds if total_seconds > 0 else 0
                self.logger.info(f"Throughput: {throughput:.2f} files/second")

            self.logger.info("=" * 60)

            return results

        except Exception as e:
            self.logger.error(f"Error processing folder {folder_path}: {e}")
            self.logger.error(traceback.format_exc())
            results["error"] = str(e)
            return results


# Utility functions for easy usage
async def process_documents_from_folder(folder_path: str,
                                      database_name: str = "dbProductionV2",
                                      collection_name: str = "collectionResumeV2",
                                      n_workers: int = 12,
                                      enable_chromadb: bool = True,
                                      chroma_host: str = "localhost",
                                      chroma_port: int = 8000) -> Dict[str, Any]:
    """
    Convenience function to process all documents in a folder with parallel processing.

    Args:
        folder_path: Path to the folder containing documents
        database_name: MongoDB database name (default: "dbProductionV2")
        collection_name: MongoDB collection name (default: "collectionResumeV2")
        n_workers: Number of parallel workers (default: 12)
        enable_chromadb: Whether to enable ChromaDB integration (default: True)
        chroma_host: ChromaDB server host (default: "localhost")
        chroma_port: ChromaDB server port (default: 8000)

    Returns:
        Dictionary containing processing results and statistics
    """
    processor = DocumentProcessor(
        database_name,
        collection_name,
        n_workers=n_workers,
        enable_chromadb=enable_chromadb,
        chroma_host=chroma_host,
        chroma_port=chroma_port
    )
    return await processor.process_folder(folder_path)


def get_file_from_gridfs(database_name: str, file_id: ObjectId, output_path: str) -> bool:
    """
    Retrieve a file from GridFS and save it to the specified path.

    Args:
        database_name: MongoDB database name
        file_id: GridFS file ID
        output_path: Path where to save the retrieved file

    Returns:
        True if successful, False otherwise
    """
    try:
        mongo_client = MongoDBClient(db_name=database_name)
        fs = gridfs.GridFS(mongo_client.db)

        # Get the file from GridFS
        grid_out = fs.get(file_id)

        # Write to output path
        with open(output_path, 'wb') as output_file:
            output_file.write(grid_out.read())

        print(f"File retrieved successfully: {output_path}")
        return True

    except Exception as e:
        print(f"Error retrieving file from GridFS: {e}")
        return False


def list_processed_documents(database_name: str = "dbProductionV2",
                           collection_name: str = "collectionResumeV2") -> List[Dict[str, Any]]:
    """
    List all processed documents in the collection.

    Args:
        database_name: MongoDB database name
        collection_name: MongoDB collection name

    Returns:
        List of documents with basic information
    """
    try:
        mongo_client = MongoDBClient(db_name=database_name)
        collection = mongo_client.db[collection_name]

        # Get all documents with basic fields
        documents = list(collection.find(
            {},
            {
                "_id": 1,
                "original_filename": 1,
                "timestamp": 1,
                "checksum": 1,
                "pdf_file_id": 1,
                "vendor_name": 1
            }
        ))

        return documents

    except Exception as e:
        print(f"Error listing documents: {e}")
        return []


# Main execution
if __name__ == "__main__":
    import argparse
    import sys

    def main():
        """Main function for command-line usage."""
        parser = argparse.ArgumentParser(
            description="Process documents from a folder and store in MongoDB",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  # Process all files in a folder with default settings (12 workers)
  python mongoDBInsertion.py /path/to/documents

  # Process with custom database, collection, and worker count
  python mongoDBInsertion.py /path/to/documents --database my-db --collection my-collection --workers 8

  # Process with maximum parallelism (adjust based on your system)
  python mongoDBInsertion.py /path/to/documents --workers 20

  # List all processed documents
  python mongoDBInsertion.py --list

  # Retrieve a file from GridFS
  python mongoDBInsertion.py --get-file 507f1f77bcf86cd799439011 --output retrieved.pdf
            """
        )

        parser.add_argument(
            "folder_path",
            default=r"\\************\user_data\PAVAN\Desktop\Gemini\resumes",
            help="Path to the folder containing documents to process"
        )

        parser.add_argument(
            "--database",
            default="dbProductionV2",
            help="MongoDB database name (default: dbProductionV2)"
        )

        parser.add_argument(
            "--collection",
            default="collectionResumeV2",
            help="MongoDB collection name (default: collectionResumeV2)"
        )

        parser.add_argument(
            "--workers",
            type=int,
            default=12,
            help="Number of parallel workers for processing (default: 12)"
        )

        parser.add_argument(
            "--list",
            action="store_true",
            help="List all processed documents"
        )

        parser.add_argument(
            "--get-file",
            help="Retrieve a file from GridFS by ObjectId"
        )

        parser.add_argument(
            "--output",
            help="Output path for retrieved file (used with --get-file)"
        )

        args = parser.parse_args()

        # Handle list command
        if args.list:
            print("Listing processed documents...")
            documents = list_processed_documents(args.database, args.collection)

            if documents:
                print(f"\nFound {len(documents)} processed documents:")
                print("-" * 80)
                for doc in documents:
                    print(f"ID: {doc['_id']}")
                    print(f"Filename: {doc.get('original_filename', 'N/A')}")
                    print(f"Processed: {doc.get('timestamp', 'N/A')}")
                    print(f"PDF File ID: {doc.get('pdf_file_id', 'N/A')}")
                    print("-" * 80)
            else:
                print("No processed documents found.")
            return

        # Handle get-file command
        if args.get_file:
            if not args.output:
                print("Error: --output is required when using --get-file")
                sys.exit(1)

            try:
                file_id = ObjectId(args.get_file)
                success = get_file_from_gridfs(args.database, file_id, args.output)
                if success:
                    print(f"File retrieved successfully: {args.output}")
                else:
                    print("Failed to retrieve file")
                    sys.exit(1)
            except Exception as e:
                print(f"Error: Invalid ObjectId or retrieval failed: {e}")
                sys.exit(1)
            return

        # Handle folder processing
        if not args.folder_path:
            print("Error: folder_path is required for processing")
            parser.print_help()
            sys.exit(1)

        if not os.path.exists(args.folder_path):
            print(f"Error: Folder does not exist: {args.folder_path}")
            sys.exit(1)

        # Process the folder
        async def run_processing():
            print(f"Processing documents from: {args.folder_path}")
            print(f"Database: {args.database}")
            print(f"Collection: {args.collection}")
            print(f"Parallel workers: {args.workers}")
            print("-" * 50)

            results = await process_documents_from_folder(
                args.folder_path,
                args.database,
                args.collection,
                args.workers
            )

            # Print results
            print("\nProcessing completed!")
            print(f"Total files: {results.get('total_files', 0)}")
            print(f"Successfully processed: {results.get('processed_successfully', 0)}")
            print(f"Failed: {results.get('failed_files', 0)}")
            print(f"Duplicates skipped: {results.get('duplicate_files', 0)}")
            print(f"Processing time: {results.get('processing_time', 'N/A')}")

            if results.get('failed_files_list'):
                print(f"\nFailed files: {', '.join(results['failed_files_list'])}")

            if results.get('duplicate_files_list'):
                print(f"\nDuplicate files: {', '.join(results['duplicate_files_list'])}")

        # Run the async processing
        try:
            asyncio.run(run_processing())
        except KeyboardInterrupt:
            print("\nProcessing interrupted by user")
            sys.exit(1)
        except Exception as e:
            print(f"Error during processing: {e}")
            sys.exit(1)

    main()
