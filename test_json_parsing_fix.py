"""
Test the JSON parsing fix for MongoDB query generation.
"""

import asyncio
from chromaMongoSearch import ChromaMongoSearchEngine

async def test_json_parsing():
    """Test JSON parsing with comments removal."""
    
    print("🔧 Testing JSON Parsing Fix")
    print("=" * 50)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Test queries that might generate comments
    test_queries = [
        "Find me a female teacher who teaches hindi",
        "Looking for male software engineers",
        "Need candidates with PhD degree",
        "Search for people from Mumbai",
        "Find candidates with 5+ years experience"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 Test {i}: {query}")
        print("-" * 40)
        
        try:
            # Test MongoDB query generation
            mongodb_query = await search_engine.generate_mongodb_search_query(query)
            
            if mongodb_query:
                print(f"✅ Successfully parsed MongoDB query:")
                print(f"   {mongodb_query}")
                
                # Test if it's valid by using it in search
                mongodb_ids = await search_engine.search_mongodb_with_generated_query(mongodb_query, limit=10)
                print(f"✅ Query executed successfully, found {len(mongodb_ids)} IDs")
                
            else:
                print("⚠️  Empty MongoDB query generated")
                
        except Exception as e:
            print(f"❌ Error: {e}")

async def test_full_search_with_fix():
    """Test full search to ensure the fix works end-to-end."""
    
    print("\n🔍 Testing Full Search with JSON Fix")
    print("=" * 50)
    
    search_engine = ChromaMongoSearchEngine()
    
    query = "Find me a female teacher who teaches hindi"
    print(f"Query: {query}")
    
    try:
        results = await search_engine.search(query, top_k=4)
        
        print(f"\n📊 Results:")
        print(f"   MongoDB Query: {results.get('mongodb_query', 'N/A')}")
        print(f"   Vector Query: {results.get('vector_search_query', 'N/A')}")
        print(f"   MongoDB IDs Found: {results.get('mongodb_ids_found', 0)}")
        print(f"   ChromaDB Docs Found: {results.get('chromadb_docs_found', 0)}")
        print(f"   Total Results: {results.get('total_results', 0)}")
        
        if results.get('results'):
            print(f"\n🏆 Top Results:")
            for result in results['results'][:2]:
                print(f"   {result['rank']}. {result['full_name']} ({result['embedding_type']})")
                print(f"      Similarity: {result['similarity_score']:.3f}")
        
        if results.get('error'):
            print(f"❌ Error: {results['error']}")
        else:
            print("✅ Full search completed successfully!")
            
    except Exception as e:
        print(f"❌ Full search failed: {e}")
        import traceback
        traceback.print_exc()

def test_json_cleaning():
    """Test the JSON cleaning function directly."""
    
    print("\n🧹 Testing JSON Cleaning Function")
    print("=" * 50)
    
    import json
    import re
    
    # Test cases with comments
    test_cases = [
        '{"field": "value"}',  # Clean JSON
        '{"field": "value"} // comment',  # Single line comment
        '''{"field": "value",
           // This is a comment
           "field2": "value2"}''',  # Multi-line with comment
        '''{"$and":[
            {"Resume.PersonalInformation.Gender": {"$regex": "female", "$options": "i"}}
            // NOTE: Name search is not allowed
        ]}''',  # The problematic case
        '''{"field": "value"} /* block comment */''',  # Block comment
    ]
    
    for i, test_json in enumerate(test_cases, 1):
        print(f"\nTest {i}:")
        print(f"Original: {repr(test_json)}")
        
        try:
            # Apply the same cleaning logic
            cleaned_query = re.sub(r'//.*$', '', test_json, flags=re.MULTILINE)
            cleaned_query = re.sub(r'/\*.*?\*/', '', cleaned_query, flags=re.DOTALL)
            cleaned_query = re.sub(r'\s+', ' ', cleaned_query).strip()
            
            print(f"Cleaned:  {repr(cleaned_query)}")
            
            # Try to parse
            parsed = json.loads(cleaned_query)
            print(f"✅ Parsed successfully: {parsed}")
            
        except json.JSONDecodeError as e:
            print(f"❌ Still failed to parse: {e}")

async def main():
    """Main test function."""
    
    print("🚀 JSON Parsing Fix Test Suite")
    print("=" * 60)
    
    # Test 1: JSON cleaning function
    test_json_cleaning()
    
    # Test 2: MongoDB query generation
    await test_json_parsing()
    
    # Test 3: Full search
    await test_full_search_with_fix()
    
    print("\n" + "=" * 60)
    print("✅ JSON Parsing Fix Tests Completed!")
    print("\nFixes Applied:")
    print("✅ Remove JavaScript-style comments (//) from JSON")
    print("✅ Remove block comments (/* */) from JSON")
    print("✅ Clean extra whitespace and newlines")
    print("✅ Better error handling and logging")
    print("✅ Improved prompt to avoid comments")

if __name__ == "__main__":
    asyncio.run(main())
