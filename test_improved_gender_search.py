"""
Test the improved gender filtering in hybrid search.
"""

import asyncio
from chromaMongoSearch import ChromaMongoSearchEngine

async def test_improved_female_search():
    """Test improved female search with verification."""
    
    print("🔍 Testing Improved Female Search")
    print("=" * 50)
    
    search_engine = ChromaMongoSearchEngine()
    query = "Find females with experience in teaching in hindi"
    
    print(f"Query: {query}")
    print()
    
    results = await search_engine.search(query, top_k=5)
    
    print("📊 Search Results:")
    print(f"   Original Query: {results['query']}")
    print(f"   Optimized Query: {results['optimized_query']}")
    print(f"   MongoDB IDs Found: {results['mongodb_ids_found']}")
    print(f"   ChromaDB Docs Found: {results['chromadb_docs_found']}")
    print(f"   Total Results: {results['total_results']}")
    print()
    
    if results.get('search_stages'):
        print("🔄 Search Stages:")
        for stage, description in results['search_stages'].items():
            print(f"   {stage}: {description}")
        print()
    
    if results['results']:
        print("🏆 Top Results:")
        for result in results['results']:
            print(f"   {result['rank']}. {result['full_name']} ({result['embedding_type']})")
            print(f"      Similarity Score: {result['similarity_score']:.3f}")
            print(f"      Match Distance: {result['match_distance']:.3f}")
            print(f"      MongoDB ID: {result['mongodb_id']}")
            print(f"      File: {result['original_filename']}")
            print()
    else:
        print("No results found.")

async def test_male_search():
    """Test male search with verification."""
    
    print("\n🔍 Testing Male Search")
    print("=" * 50)
    
    search_engine = ChromaMongoSearchEngine()
    query = "Find male software engineers with Python experience"
    
    print(f"Query: {query}")
    print()
    
    results = await search_engine.search(query, top_k=3)
    
    print("📊 Search Results:")
    print(f"   Total Results: {results['total_results']}")
    print(f"   MongoDB IDs Found: {results['mongodb_ids_found']}")
    print()
    
    if results['results']:
        print("🏆 Top Results:")
        for result in results['results']:
            print(f"   {result['rank']}. {result['full_name']} ({result['embedding_type']})")
            print(f"      Similarity: {result['similarity_score']:.3f}")
            print()

async def compare_searches():
    """Compare female vs male searches."""
    
    print("\n📊 Comparing Female vs Male Searches")
    print("=" * 50)
    
    search_engine = ChromaMongoSearchEngine()
    
    queries = [
        "Find females with teaching experience",
        "Find males with teaching experience"
    ]
    
    for query in queries:
        print(f"\nQuery: {query}")
        results = await search_engine.search(query, top_k=3)
        
        print(f"Results: {results['total_results']}")
        print(f"MongoDB IDs: {results['mongodb_ids_found']}")
        
        if results['results']:
            print("Top matches:")
            for result in results['results'][:2]:
                print(f"  - {result['full_name']} (Score: {result['similarity_score']:.3f})")

async def main():
    """Main test function."""
    
    print("🚀 Improved Gender Filtering Test")
    print("=" * 60)
    
    await test_improved_female_search()
    await test_male_search()
    await compare_searches()
    
    print("\n" + "=" * 60)
    print("✅ Improved gender filtering test completed!")
    print("\nKey improvements:")
    print("✅ MongoDB filtering by gender")
    print("✅ Post-processing verification")
    print("✅ Consistent gender results")
    print("✅ Better query optimization")

if __name__ == "__main__":
    asyncio.run(main())
