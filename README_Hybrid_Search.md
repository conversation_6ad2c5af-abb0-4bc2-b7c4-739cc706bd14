# ChromaDB + MongoDB Hybrid Search System

A sophisticated two-stage search system that combines MongoDB filtering with ChromaDB vector search for intelligent resume matching using natural language queries.

## Overview

The hybrid search system implements a unique approach:

1. **Stage 1**: MongoDB search excluding embedding factors to get candidate MongoDB IDs
2. **Stage 2**: ChromaDB vector search on filtered documents using OpenAI embeddings
3. **Stage 3**: OpenAI query processing and similarity scoring with match distances

## Key Features

### ✅ **Two-Stage Search Process**
- MongoDB filtering excludes embedding-related fields from search criteria
- ChromaDB vector search focuses on semantic similarity
- Combines structured filtering with AI-powered matching

### ✅ **OpenAI Integration**
- Query optimization using GPT models
- Embedding generation for vector search
- Natural language processing for better search understanding

### ✅ **Comprehensive Results**
- Similarity scores and match distances
- MongoDB ID cross-referencing
- Detailed metadata and document previews
- Ranking by relevance

### ✅ **15 Embedding Columns**
The system searches across these embedding types:
- FullNameEmbedding
- InstitutionEmbedding
- CompanyNameEmbedding
- RoleEmbedding
- Description/ResponsibilityEmbedding
- SkillsEmbedding
- CertificationNameEmbedding
- IssuingOrganizationEmbedding
- AchievementNameEmbedding
- ProjectNameEmbedding
- DescriptionEmbedding
- TechnologiesUsedEmbedding
- ProjectRoleEmbedding

## Installation & Setup

### Prerequisites
```bash
pip install openai chromadb pymongo
```

### Configuration
```python
from chromaMongoSearch import ChromaMongoSearchEngine

# Basic setup
search_engine = ChromaMongoSearchEngine(
    database_name="dbProductionV2",
    collection_name="collectionResumeV2",
    chroma_host="localhost",
    chroma_port=8000
)
```

## Usage Examples

### Basic Search
```python
import asyncio
from chromaMongoSearch import search_resumes

async def basic_search():
    results = await search_resumes(
        "Find software engineers with Python experience", 
        top_k=5
    )
    
    print(f"Found {results['total_results']} results")
    for result in results['results']:
        print(f"{result['rank']}. {result['full_name']} - {result['similarity_score']:.3f}")

asyncio.run(basic_search())
```

### Advanced Search
```python
from chromaMongoSearch import ChromaMongoSearchEngine

async def advanced_search():
    search_engine = ChromaMongoSearchEngine()
    
    results = await search_engine.search(
        "Looking for data scientists with machine learning background",
        top_k=10
    )
    
    # Analyze results
    print(f"Original Query: {results['query']}")
    print(f"Optimized Query: {results['optimized_query']}")
    print(f"MongoDB IDs Found: {results['mongodb_ids_found']}")
    print(f"ChromaDB Docs Found: {results['chromadb_docs_found']}")
    
    for result in results['results']:
        print(f"{result['full_name']} ({result['embedding_type']})")
        print(f"  Similarity: {result['similarity_score']:.3f}")
        print(f"  Distance: {result['match_distance']:.3f}")
        print(f"  MongoDB ID: {result['mongodb_id']}")
```

## Search Process Flow

```
User Query: "Find Python developers with ML experience"
     ↓
[OpenAI Query Processing]
     ↓
Optimized Query: "Python developer machine learning"
     ↓
[MongoDB Search - Excluding Embedding Fields]
     ↓
MongoDB IDs: [id1, id2, id3, ...]
     ↓
[ChromaDB Filtering by MongoDB IDs]
     ↓
Filtered ChromaDB Documents
     ↓
[Query Embedding Generation]
     ↓
Query Vector: [0.123, -0.456, 0.789, ...]
     ↓
[Vector Similarity Search]
     ↓
Ranked Results with Similarity Scores
```

## Result Format

```json
{
  "query": "Find software engineers with Python experience",
  "optimized_query": "Software Engineer Python",
  "mongodb_ids_found": 12,
  "chromadb_docs_found": 81,
  "total_results": 5,
  "results": [
    {
      "rank": 1,
      "chromadb_id": "mongo_6853f1ab_12345678_FullNameEmbedding",
      "mongodb_id": "6853f1abe12db570f0418d32",
      "full_name": "Sheikh Wasim",
      "email": "<EMAIL>",
      "embedding_type": "Role",
      "match_distance": 1.335,
      "similarity_score": -0.335,
      "document_preview": "Software Engineer with Python...",
      "original_filename": "resume.pdf"
    }
  ],
  "search_stages": {
    "stage_1": "NLP query processed with OpenAI",
    "stage_2": "Found 12 MongoDB IDs",
    "stage_3": "Found 81 ChromaDB documents",
    "stage_4": "Query embedding generated",
    "stage_5": "Vector search returned 5 results"
  }
}
```

## Excluded MongoDB Fields

The system excludes these fields from MongoDB search to focus on vector similarity:

- `Resume.PersonalInformation.FullName`
- `Resume.Education.Institution`
- `Resume.WorkExperience.CompanyName`
- `Resume.WorkExperience.Role`
- `Resume.WorkExperience.Description/Responsibility`
- `Resume.Skills`
- `Resume.Certifications.CertificationName`
- `Resume.Certifications.IssuingOrganization`
- `Resume.Achievements.AchievementName`
- `Resume.Projects.ProjectName`
- `Resume.Projects.Description`
- `Resume.Projects.TechnologiesUsed`
- `Resume.Projects.Role`

## Search Tips

### ✅ **Effective Queries**
- "Find software engineers with Python experience"
- "Looking for data scientists with machine learning background"
- "Need frontend developers with React and JavaScript skills"
- "Search for project managers with agile methodology experience"

### ✅ **Query Optimization**
- Use specific job titles
- Include relevant technologies and skills
- Mention experience levels
- Add domain expertise
- Include certifications

### ❌ **Avoid**
- Very generic terms
- Overly complex sentences
- Spelling mistakes in technical terms
- Using only company/personal names

## Performance Metrics

Based on test results:
- **MongoDB Search**: ~12 IDs found from database
- **ChromaDB Filtering**: ~81 documents retrieved
- **Vector Search**: Top 5-10 results with similarity scores
- **Query Processing**: ~2-3 seconds total time
- **Accuracy**: High relevance with semantic matching

## Testing

Run the test suite:
```bash
python test_hybrid_search.py
```

Run examples:
```bash
python example_hybrid_search.py
```

## API Reference

### ChromaMongoSearchEngine

#### `__init__(database_name, collection_name, chroma_host, chroma_port, ...)`
Initialize the search engine with database connections.

#### `async search(nlp_query, top_k=10, mongodb_limit=1000)`
Main search function that orchestrates the hybrid search process.

#### `search_mongodb_excluding_embedding_fields(limit=1000)`
Get MongoDB IDs excluding embedding-related fields.

#### `get_chromadb_docs_by_mongodb_ids(mongodb_ids)`
Filter ChromaDB documents by MongoDB IDs.

#### `perform_vector_search(query_embedding, chromadb_results, top_k=10)`
Perform vector similarity search with ranking.

### Convenience Functions

#### `async search_resumes(query, top_k=10)`
Simple function for quick searches.

## Error Handling

The system includes comprehensive error handling:
- MongoDB connection failures
- ChromaDB server issues
- OpenAI API errors
- Invalid query formats
- Empty result sets

## Logging

Detailed logging for debugging:
- Search stages and timing
- Result counts at each stage
- Error messages and stack traces
- Performance metrics

## Integration

The hybrid search integrates seamlessly with:
- Existing MongoDB resume database
- ChromaDB vector storage
- OpenAI embedding services
- Resume processing pipeline

## Future Enhancements

Potential improvements:
- Caching for frequently used queries
- Advanced filtering options
- Real-time search suggestions
- Batch processing capabilities
- Custom embedding models
