"""
Test the corrected hybrid search approach:
1. MongoDB search on NON-embedding fields only
2. ChromaDB search on the 13 embedding fields only
3. Return best 4 matches
"""

import asyncio
from chromaMongoSearch import ChromaMongoSearchEngine, search_resumes

async def test_corrected_approach():
    """Test the corrected search approach."""
    
    print("🔍 Testing Corrected Hybrid Search Approach")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Test the female teaching query that was problematic before
    query = "Find females with experience in teaching in hindi"
    
    print(f"Query: {query}")
    print()
    
    # Test step by step
    print("Step 1: MongoDB search on NON-embedding fields")
    mongodb_ids = search_engine.search_mongodb_on_non_embedding_fields(query, limit=100)
    print(f"   Found {len(mongodb_ids)} MongoDB IDs")
    if mongodb_ids:
        print(f"   Sample IDs: {mongodb_ids[:3]}")
    print()
    
    print("Step 2: ChromaDB search on 13 embedding fields only")
    chromadb_results = search_engine.get_chromadb_docs_by_mongodb_ids(mongodb_ids)
    print(f"   Found {len(chromadb_results.get('ids', []))} ChromaDB documents")
    
    if chromadb_results.get('metadatas'):
        embedding_types = set(meta.get('embedding_type', 'Unknown') for meta in chromadb_results['metadatas'])
        print(f"   Embedding types found: {sorted(list(embedding_types))}")
    print()
    
    print("Step 3: Full search with vector similarity")
    results = await search_engine.search(query, top_k=4)
    
    print("📊 Final Results:")
    print(f"   Total Results: {results['total_results']}")
    print(f"   MongoDB IDs: {results['mongodb_ids_found']}")
    print(f"   ChromaDB Docs: {results['chromadb_docs_found']}")
    print()
    
    if results['results']:
        print("🏆 Top 4 Results:")
        for result in results['results']:
            print(f"   {result['rank']}. {result['full_name']} ({result['embedding_type']})")
            print(f"      Similarity: {result['similarity_score']:.3f}")
            print(f"      Distance: {result['match_distance']:.3f}")
            print(f"      MongoDB ID: {result['mongodb_id']}")
            print()
    
    if results.get('search_stages'):
        print("🔄 Search Stages:")
        for stage, description in results['search_stages'].items():
            print(f"   {stage}: {description}")

async def test_multiple_queries():
    """Test multiple different queries."""
    
    print("\n🧪 Testing Multiple Queries")
    print("=" * 60)
    
    queries = [
        "Find females with experience in teaching in hindi",
        "Looking for software engineers with Python",
        "Need data scientists with machine learning",
        "Search for project managers",
        "Find candidates with biotechnology background"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n📋 Query {i}: {query}")
        print("-" * 40)
        
        try:
            results = await search_resumes(query)  # Uses default top_k=4
            
            print(f"Results: {results['total_results']}")
            print(f"MongoDB IDs: {results['mongodb_ids_found']}")
            print(f"ChromaDB Docs: {results['chromadb_docs_found']}")
            
            if results['results']:
                print("Top matches:")
                for result in results['results'][:2]:  # Show top 2
                    print(f"  - {result['full_name']} (Score: {result['similarity_score']:.3f})")
            
        except Exception as e:
            print(f"Error: {e}")

async def test_embedding_field_focus():
    """Test that we're only searching the 13 embedding fields."""
    
    print("\n🎯 Testing 13 Embedding Fields Focus")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Get some MongoDB IDs
    mongodb_ids = search_engine.search_mongodb_on_non_embedding_fields("software engineer", limit=50)
    print(f"MongoDB IDs found: {len(mongodb_ids)}")
    
    # Get ChromaDB results
    chromadb_results = search_engine.get_chromadb_docs_by_mongodb_ids(mongodb_ids)
    print(f"ChromaDB documents: {len(chromadb_results.get('ids', []))}")
    
    if chromadb_results.get('metadatas'):
        # Analyze embedding types
        embedding_types = {}
        for meta in chromadb_results['metadatas']:
            emb_type = meta.get('embedding_type', 'Unknown')
            embedding_types[emb_type] = embedding_types.get(emb_type, 0) + 1
        
        print("\n📊 Embedding Types Distribution:")
        for emb_type, count in sorted(embedding_types.items()):
            print(f"   {emb_type}: {count} documents")
        
        # Verify we only have the 13 target embedding types
        target_types = {
            "FullName", "Institution", "CompanyName", "Role", 
            "Description/Responsibility", "Skills", "CertificationName",
            "IssuingOrganization", "AchievementName", "ProjectName",
            "Description", "TechnologiesUsed", "ProjectRole"
        }
        
        found_types = set(embedding_types.keys())
        
        print(f"\n✅ Target embedding types: {len(target_types)}")
        print(f"✅ Found embedding types: {len(found_types)}")
        print(f"✅ Match: {found_types.issubset(target_types)}")
        
        if not found_types.issubset(target_types):
            extra_types = found_types - target_types
            print(f"⚠️  Extra types found: {extra_types}")

async def test_non_embedding_fields():
    """Test what fields MongoDB is actually searching."""
    
    print("\n📋 Testing Non-Embedding Fields Search")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Test different types of queries
    test_cases = [
        ("female", "Gender-based search"),
        ("gmail.com", "Email-based search"), 
        ("bachelor", "Education-based search"),
        ("5 years", "Experience-based search"),
        ("mumbai", "Location-based search")
    ]
    
    for query, description in test_cases:
        print(f"\n{description}: '{query}'")
        mongodb_ids = search_engine.search_mongodb_on_non_embedding_fields(query, limit=20)
        print(f"   MongoDB IDs found: {len(mongodb_ids)}")

async def main():
    """Main test function."""
    
    print("🚀 Corrected Hybrid Search Test Suite")
    print("=" * 80)
    
    # Test 1: Corrected approach step by step
    await test_corrected_approach()
    
    # Test 2: Multiple queries
    await test_multiple_queries()
    
    # Test 3: Embedding field focus
    await test_embedding_field_focus()
    
    # Test 4: Non-embedding fields
    await test_non_embedding_fields()
    
    print("\n" + "=" * 80)
    print("✅ Corrected Search Tests Completed!")
    print("\nKey Changes:")
    print("✅ MongoDB searches only NON-embedding fields")
    print("✅ ChromaDB searches only the 13 embedding fields")
    print("✅ No explicit filters - natural language processing")
    print("✅ Returns best 4 matches by default")
    print("✅ Pure vector similarity on embedding fields")

if __name__ == "__main__":
    asyncio.run(main())
