"""
Test script to demonstrate the MongoDB + ChromaDB integration.

This script shows how the new integrated workflow works:
1. Process documents with mongoDBInsertion.py
2. Automatically store embeddings in ChromaDB with MongoDB IDs
3. Query ChromaDB to verify the integration
"""

import asyncio
import os
from mongoDBInsertion import DocumentProcessor, process_documents_from_folder
from chromdb_processor import ChromaDBProcessor
from bson import ObjectId

async def test_integration():
    """Test the MongoDB + ChromaDB integration."""
    
    print("🚀 Testing MongoDB + ChromaDB Integration")
    print("=" * 50)
    
    # Configuration
    database_name = "dbProductionV2"
    collection_name = "collectionResumeV2"
    test_folder = "./Data/inputData/Resume_Schema"  # Adjust path as needed
    
    # Check if test folder exists
    if not os.path.exists(test_folder):
        print(f"❌ Test folder not found: {test_folder}")
        print("Please ensure you have some test documents in the folder.")
        return
    
    # List files in test folder
    files = [f for f in os.listdir(test_folder) if f.lower().endswith(('.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'))]
    if not files:
        print(f"❌ No supported files found in: {test_folder}")
        print("Supported formats: PDF, DOC, DOCX, JPG, JPEG, PNG")
        return
    
    print(f"📁 Found {len(files)} files to process:")
    for file in files[:5]:  # Show first 5 files
        print(f"   - {file}")
    if len(files) > 5:
        print(f"   ... and {len(files) - 5} more files")
    
    print("\n🔄 Starting document processing...")
    
    try:
        # Process documents with ChromaDB integration enabled
        results = await process_documents_from_folder(
            folder_path=test_folder,
            database_name=database_name,
            collection_name=collection_name,
            n_workers=4,  # Use fewer workers for testing
            enable_chromadb=True,
            chroma_host="localhost",
            chroma_port=8000
        )
        
        print("\n📊 Processing Results:")
        print(f"   ✅ Successfully processed: {results.get('successful', 0)}")
        print(f"   ⚠️  Skipped (duplicates): {results.get('skipped', 0)}")
        print(f"   ❌ Failed: {results.get('failed', 0)}")
        print(f"   ⏱️  Total time: {results.get('total_time', 0):.2f} seconds")
        
        if results.get('successful', 0) > 0:
            print("\n🔍 Testing ChromaDB integration...")
            
            # Initialize ChromaDB processor to query data
            chromadb_processor = ChromaDBProcessor()
            
            # Get some sample data from ChromaDB
            try:
                # Query ChromaDB collection
                sample_results = chromadb_processor.collection.get(limit=5)
                
                if sample_results and sample_results['ids']:
                    print(f"✅ Found {len(sample_results['ids'])} embeddings in ChromaDB")
                    
                    # Show sample metadata
                    for i, (doc_id, metadata) in enumerate(zip(sample_results['ids'], sample_results['metadatas'])):
                        print(f"\n📄 Sample {i+1}:")
                        print(f"   ID: {doc_id}")
                        print(f"   MongoDB ID: {metadata.get('mongodb_id', 'N/A')}")
                        print(f"   Full Name: {metadata.get('FullName', 'N/A')}")
                        print(f"   Email: {metadata.get('Email', 'N/A')}")
                        print(f"   Embedding Type: {metadata.get('embedding_type', 'N/A')}")
                        print(f"   Original Filename: {metadata.get('original_filename', 'N/A')}")
                        
                        if i >= 2:  # Show only first 3 samples
                            break
                    
                    print("\n✅ Integration test completed successfully!")
                    print("🎉 MongoDB IDs are now included in ChromaDB metadata!")
                    
                else:
                    print("⚠️  No embeddings found in ChromaDB")
                    
            except Exception as e:
                print(f"❌ Error querying ChromaDB: {e}")
        
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        import traceback
        traceback.print_exc()

def test_chromadb_processor():
    """Test the ChromaDB processor independently."""
    
    print("\n🧪 Testing ChromaDB Processor independently...")
    print("=" * 50)
    
    # Sample structured data (like what comes from MongoDB)
    sample_structured_data = {
        "Resume": {
            "PersonalInformation": {
                "FullName": "John Doe",
                "Email": "<EMAIL>",
                "ContactNumber": "1234567890",
                "Address": "123 Main St, City, State"
            },
            "Skills": ["Python", "Machine Learning", "Data Analysis"],
            "WorkExperience": [
                {
                    "CompanyName": "Tech Corp",
                    "Role": "Software Engineer",
                    "Description/Responsibility": "Developed web applications"
                }
            ],
            "Education": [
                {
                    "Institution": "University of Technology",
                    "Degree": "Bachelor of Science"
                }
            ]
        }
    }
    
    # Create a fake MongoDB ID for testing
    fake_mongodb_id = ObjectId()
    
    try:
        # Initialize ChromaDB processor
        processor = ChromaDBProcessor()
        
        # Process the sample data
        success = processor.process_structured_data(
            structured_data=sample_structured_data,
            mongodb_id=fake_mongodb_id,
            original_filename="test_resume.pdf"
        )
        
        if success:
            print("✅ Successfully processed sample data")
            
            # Query to verify
            results = processor.collection.get(
                where={"mongodb_id": str(fake_mongodb_id)},
                limit=10
            )
            
            if results and results['ids']:
                print(f"✅ Found {len(results['ids'])} embeddings for test MongoDB ID")
                
                # Clean up test data
                processor.delete_by_mongodb_id(fake_mongodb_id)
                print("🧹 Cleaned up test data")
                
            else:
                print("⚠️  No embeddings found for test MongoDB ID")
        else:
            print("❌ Failed to process sample data")
            
    except Exception as e:
        print(f"❌ Error testing ChromaDB processor: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔬 MongoDB + ChromaDB Integration Test")
    print("=" * 60)
    
    # Test ChromaDB processor first
    test_chromadb_processor()
    
    # Then test full integration
    print("\n" + "=" * 60)
    asyncio.run(test_integration())
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")
