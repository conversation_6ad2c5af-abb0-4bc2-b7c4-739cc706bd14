"""
Test the corrected two-OpenAI-call approach:
1. First OpenAI call: Generate MongoDB query for non-embedding fields
2. Second OpenAI call: Generate ChromaDB vector search query for 13 embedding fields
"""

import asyncio
from chromaMongoSearch import ChromaMongoSearchEngine

async def test_two_openai_calls():
    """Test the two OpenAI calls approach."""
    
    print("🔍 Testing Two OpenAI Calls Approach")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Test query
    user_query = "Find me a female teacher who teaches hindi"
    
    print(f"User Query: {user_query}")
    print()
    
    # Test step by step
    print("Step 1: First OpenAI Call - Generate MongoDB Query")
    mongodb_query = await search_engine.generate_mongodb_search_query(user_query)
    print(f"   Generated MongoDB Query: {mongodb_query}")
    print()
    
    print("Step 2: Search MongoDB with Generated Query")
    mongodb_ids = await search_engine.search_mongodb_with_generated_query(mongodb_query, limit=50)
    print(f"   MongoDB IDs Found: {len(mongodb_ids)}")
    if mongodb_ids:
        print(f"   Sample IDs: {mongodb_ids[:3]}")
    print()
    
    print("Step 3: Filter ChromaDB by MongoDB IDs (13 embedding fields only)")
    chromadb_results = search_engine.get_chromadb_docs_by_mongodb_ids(mongodb_ids)
    print(f"   ChromaDB Documents Found: {len(chromadb_results.get('ids', []))}")
    
    if chromadb_results.get('metadatas'):
        embedding_types = set(meta.get('embedding_type', 'Unknown') for meta in chromadb_results['metadatas'])
        print(f"   Embedding Types: {sorted(list(embedding_types))}")
    print()
    
    print("Step 4: Second OpenAI Call - Generate ChromaDB Vector Search Query")
    vector_search_query = await search_engine.generate_chromadb_search_query(user_query)
    print(f"   Generated Vector Search Query: {vector_search_query}")
    print()
    
    print("Step 5: Generate Embedding for Vector Search")
    query_embedding = search_engine.get_embedding(vector_search_query)
    if query_embedding:
        print(f"   Embedding Generated: {len(query_embedding)} dimensions")
        print(f"   Sample values: {query_embedding[:5]}")
    else:
        print("   Failed to generate embedding")
    print()
    
    print("Step 6: Full Search with Two OpenAI Calls")
    results = await search_engine.search(user_query, top_k=4)
    
    print("📊 Final Results:")
    print(f"   Original Query: {results['query']}")
    print(f"   MongoDB Query: {results.get('mongodb_query', 'N/A')}")
    print(f"   Vector Search Query: {results.get('vector_search_query', 'N/A')}")
    print(f"   MongoDB IDs Found: {results['mongodb_ids_found']}")
    print(f"   ChromaDB Docs Found: {results['chromadb_docs_found']}")
    print(f"   Total Results: {results['total_results']}")
    print()
    
    if results['results']:
        print("🏆 Top 4 Results:")
        for result in results['results']:
            print(f"   {result['rank']}. {result['full_name']} ({result['embedding_type']})")
            print(f"      Similarity: {result['similarity_score']:.3f}")
            print(f"      Distance: {result['match_distance']:.3f}")
            print(f"      MongoDB ID: {result['mongodb_id']}")
            print(f"      Preview: {result['document_preview'][:100]}...")
            print()
    
    if results.get('search_stages'):
        print("🔄 Search Stages:")
        for stage, description in results['search_stages'].items():
            print(f"   {stage}: {description}")

async def test_different_queries():
    """Test different types of queries with two OpenAI calls."""
    
    print("\n🧪 Testing Different Query Types")
    print("=" * 60)
    
    queries = [
        "Find females with teaching experience",
        "Find femle who have a skill of being optimistic"
    ]
    
    search_engine = ChromaMongoSearchEngine()
    
    for i, query in enumerate(queries, 1):
        print(f"\n📋 Query {i}: {query}")
        print("-" * 50)
        
        try:
            # Test the two OpenAI calls
            mongodb_query = await search_engine.generate_mongodb_search_query(query)
            vector_query = await search_engine.generate_chromadb_search_query(query)
            
            print(f"MongoDB Query: {mongodb_query}")
            print(f"Vector Query: {vector_query}")
            
            # Test full search
            results = await search_engine.search(query, top_k=4)
            print(f"Results: {results['total_results']} | MongoDB IDs: {results['mongodb_ids_found']} | ChromaDB Docs: {results['chromadb_docs_found']}")
            
            if results['results']:
                top_result = results['results'][0]
                print(f"Top Match: {top_result['full_name']} (Score: {top_result['similarity_score']:.3f})")
            
        except Exception as e:
            print(f"Error: {e}")

async def test_openai_query_generation():
    """Test OpenAI query generation in isolation."""
    
    print("\n🤖 Testing OpenAI Query Generation")
    print("=" * 60)
    
    search_engine = ChromaMongoSearchEngine()
    
    test_cases = [
        "Find females with teaching experience",
        "Find femle who have a skill of being optimistic",
        # "Find a male engineer with chemistry experience",
        # "Find people with gmail email addresses"
    ]
    
    for query in test_cases:
        print(f"\nUser Query: {query}")
        
        # Test MongoDB query generation
        mongodb_query = await search_engine.generate_mongodb_search_query(query)
        print(f"MongoDB Query: {mongodb_query}")
        
        # Test ChromaDB query generation
        vector_query = await search_engine.generate_chromadb_search_query(query)
        print(f"Vector Query: {vector_query}")

async def main():
    """Main test function."""
    
    print("🚀 Two OpenAI Calls Hybrid Search Test")
    print("=" * 80)
    
    # Test 1: Step by step two OpenAI calls
    # await test_two_openai_calls()
    
    # Test 2: Different query types
    await test_different_queries()
    
    # Test 3: OpenAI query generation
    # await test_openai_query_generation()
    
    print("\n" + "=" * 80)
    print("✅ Two OpenAI Calls Test Completed!")
    print("\nCorrect Approach:")
    print("1️⃣ First OpenAI Call: Generate MongoDB query for non-embedding fields")
    print("2️⃣ MongoDB Search: Get IDs based on structural data (Gender, Email, etc.)")
    print("3️⃣ ChromaDB Filter: Get documents for those IDs (13 embedding fields only)")
    print("4️⃣ Second OpenAI Call: Generate vector search query for embedding fields")
    print("5️⃣ Vector Search: Semantic similarity on the 13 embedding fields")
    print("6️⃣ Results: Best 4 matches with similarity scores")

if __name__ == "__main__":
    asyncio.run(main())
