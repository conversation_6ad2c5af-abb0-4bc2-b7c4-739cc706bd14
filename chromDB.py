"""
DEPRECATED: This file is now deprecated in favor of the integrated workflow.

Use chromdb_processor.py and mongoDBInsertion.py for the new integrated approach where:
1. Documents are processed and stored in MongoDB
2. MongoDB IDs are automatically added to structured_data
3. Embeddings are stored in ChromaDB with MongoDB IDs in metadata

For migration, use the new integrated workflow instead of this standalone script.
"""

import os
import json
from uuid import uuid4
from openai import OpenAI
from chromadb import HttpClient

print("⚠️  WARNING: This script is deprecated!")
print("🔄 Use the new integrated workflow with mongoDBInsertion.py + chromdb_processor.py")
print("📖 See example_usage.py for examples of the new integrated approach")
print()

# Initialize OpenAI and Chroma clients
client_openai = OpenAI()
client = HttpClient(host="localhost", port=8000)
collection = client.get_or_create_collection("resumes_by_type")

# Folder setup
input_folder = "./Sample"
output_folder = "./Processed"
os.makedirs(output_folder, exist_ok=True)

# Embedding function
def get_embedding(text: str):
    if not text or not isinstance(text, str) or not text.strip():
        return None
    try:
        response = client_openai.embeddings.create(
            input=text.strip(),
            model="text-embedding-3-small"
        )
        return response.data[0].embedding
    except Exception as e:
        print(f"⚠️ Embedding error for '{text[:40]}...': {e}")
        return None

# Flatten metadata helper
def flatten_dict(d, parent_key='', sep='_'):
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        elif isinstance(v, list):
            if all(isinstance(i, dict) for i in v):
                joined = " | ".join(", ".join(f"{ik}:{iv}" for ik, iv in i.items()) for i in v)
                items.append((new_key, joined))
            else:
                items.append((new_key, ", ".join(str(i) for i in v)))
        else:
            items.append((new_key, v))
    return dict(items)

# Fields to embed
embedding_keys = {
    "FullNameEmbedding": lambda d: d["Resume"]["PersonalInformation"].get("FullName", ""),
    "InstitutionEmbedding": lambda d: " ".join(
        edu.get("Institution", "") for edu in d["Resume"].get("Education", []) if edu.get("Institution")
    ),
    "CompanyNameEmbedding": lambda d: " ".join(
        exp.get("CompanyName", "") for exp in d["Resume"].get("WorkExperience", []) if exp.get("CompanyName")
    ),
    "RoleEmbedding": lambda d: " ".join(
        exp.get("Role", "") for exp in d["Resume"].get("WorkExperience", []) if exp.get("Role")
    ),
    "Description/ResponsibilityEmbedding": lambda d: " ".join(
        exp.get("Description/Responsibility", "") for exp in d["Resume"].get("WorkExperience", []) if exp.get("Description/Responsibility")
    ),
    "SkillsEmbedding": lambda d: ", ".join(d["Resume"].get("Skills", [])) if isinstance(d["Resume"].get("Skills", []), list) else d["Resume"].get("Skills", ""),
    "CertificationNameEmbedding": lambda d: " ".join(
        cert.get("CertificationName", "") for cert in d["Resume"].get("Certifications", []) if cert.get("CertificationName")
    ),
    "IssuingOrganizationEmbedding": lambda d: " ".join(
        cert.get("IssuingOrganization", "") for cert in d["Resume"].get("Certifications", []) if cert.get("IssuingOrganization")
    ),
    "AchievementNameEmbedding": lambda d: " ".join(
        ach.get("AchievementName", "") for ach in d["Resume"].get("Achievements", []) if ach.get("AchievementName")
    ),
    "ProjectNameEmbedding": lambda d: " ".join(
        proj.get("ProjectName", "") for proj in d["Resume"].get("Projects", []) if proj.get("ProjectName")
    ),
    "DescriptionEmbedding": lambda d: " ".join(
        proj.get("Description", "") for proj in d["Resume"].get("Projects", []) if proj.get("Description")
    ),
    "TechnologiesUsedEmbedding": lambda d: " ".join(
        " ".join(proj.get("TechnologiesUsed", [])) for proj in d["Resume"].get("Projects", []) if proj.get("TechnologiesUsed")
    ),
    "ProjectRoleEmbedding": lambda d: " ".join(
        proj.get("Role", "") for proj in d["Resume"].get("Projects", []) if proj.get("Role")
    ),
}

# Process resumes
for file in os.listdir(input_folder):
    if file.endswith(".json"):
        with open(os.path.join(input_folder, file), 'r', encoding='utf-8') as f:
            data = json.load(f)
            resume = data.get("Resume", {})
            flat_metadata = flatten_dict(resume)

            resume_id = f"{os.path.splitext(file)[0]}_{uuid4().hex[:8]}"
            basic_info = {
                "FullName": resume.get("PersonalInformation", {}).get("FullName", ""),
                "Email": resume.get("PersonalInformation", {}).get("Email", ""),
                "resume_id": resume_id
            }

            stored_count = 0

            # Process each embedding type
            for emb_type, extractor in embedding_keys.items():
                text = extractor(data)
                vector = get_embedding(text)

                if vector:
                    collection.add(
                        ids=[f"{resume_id}_{emb_type}"],
                        documents=[text],
                        embeddings=[vector],
                        metadatas=[{
                            **basic_info,
                            "embedding_type": emb_type.replace("Embedding", ""),
                            "source_text": text
                        }]
                    )
                    stored_count += 1

            # Save processed file for record
            with open(os.path.join(output_folder, file), "w", encoding="utf-8") as out_f:
                json.dump(resume, out_f, indent=2, ensure_ascii=False)

            print(f"✅ Stored {stored_count} embeddings for {file}")

print("🎉 All resumes processed and saved to ChromaDB.")

print("\n" + "="*60)
print("🔄 MIGRATION NOTICE:")
print("This standalone script is deprecated. For new projects, use:")
print("1. mongoDBInsertion.py - for document processing with MongoDB + ChromaDB integration")
print("2. chromdb_processor.py - for ChromaDB operations with MongoDB IDs")
print("3. example_usage.py - for usage examples")
print()
print("Benefits of the new integrated approach:")
print("✅ MongoDB IDs automatically included in ChromaDB metadata")
print("✅ No need to read/write JSON files")
print("✅ Consistent data between MongoDB and ChromaDB")
print("✅ Better error handling and logging")
print("="*60)
