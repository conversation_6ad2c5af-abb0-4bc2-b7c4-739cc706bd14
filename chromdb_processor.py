"""
ChromaDB Processor Module

This module processes structured resume data and stores embeddings in ChromaDB.
It's designed to work with MongoDB-stored resume data and includes MongoDB IDs in metadata.
"""

import json
from typing import Dict, Any, Optional
from uuid import uuid4
from openai import OpenAI
from chromadb import HttpClient
from bson import ObjectId
import logging

class ChromaDBProcessor:
    """
    A processor that takes structured resume data and stores embeddings in ChromaDB.
    Designed to work with MongoDB ObjectIds and structured data.
    """
    
    def __init__(self, 
                 chroma_host: str = "localhost", 
                 chroma_port: int = 8000,
                 collection_name: str = "resumes_by_type",
                 openai_api_key: Optional[str] = None,
                 openai_base_url: Optional[str] = None):
        """
        Initialize the ChromaDB processor.
        
        Args:
            chroma_host: ChromaDB server host
            chroma_port: ChromaDB server port
            collection_name: ChromaDB collection name
            openai_api_key: OpenAI API key (optional, uses default if None)
            openai_base_url: OpenAI base URL (optional, uses default if None)
        """
        self.chroma_host = chroma_host
        self.chroma_port = chroma_port
        self.collection_name = collection_name
        
        # Initialize OpenAI client
        if openai_api_key and openai_base_url:
            self.openai_client = OpenAI(api_key=openai_api_key, base_url=openai_base_url)
        else:
            self.openai_client = OpenAI()  # Uses default configuration
        
        # Initialize ChromaDB client
        self.chroma_client = HttpClient(host=chroma_host, port=chroma_port)
        self.collection = self.chroma_client.get_or_create_collection(collection_name)
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Define embedding extraction functions
        self.embedding_keys = {
            "FullNameEmbedding": lambda d: d["Resume"]["PersonalInformation"].get("FullName", ""),
            "InstitutionEmbedding": lambda d: [
                edu.get("Institution", "") for edu in d["Resume"].get("Education", []) if edu.get("Institution")
            ],
            "CompanyNameEmbedding": lambda d: [
                exp.get("CompanyName", "") for exp in d["Resume"].get("WorkExperience", []) if exp.get("CompanyName")
            ],
            "RoleEmbedding": lambda d: [
                exp.get("Role", "") for exp in d["Resume"].get("WorkExperience", []) if exp.get("Role")
            ],
            "Description/ResponsibilityEmbedding": lambda d: [
                exp.get("Description/Responsibility", "") for exp in d["Resume"].get("WorkExperience", []) if exp.get("Description/Responsibility")
            ],
            "SkillsEmbedding": lambda d: ", ".join(d["Resume"].get("Skills", [])) if isinstance(d["Resume"].get("Skills", []), list) else d["Resume"].get("Skills", ""),
            "CertificationNameEmbedding": lambda d: [
                cert.get("CertificationName", "") for cert in d["Resume"].get("Certifications", []) if cert.get("CertificationName")
            ],
            "IssuingOrganizationEmbedding": lambda d: [
                cert.get("IssuingOrganization", "") for cert in d["Resume"].get("Certifications", []) if cert.get("IssuingOrganization")
            ],
            "AchievementNameEmbedding": lambda d: [
                ach.get("AchievementName", "") for ach in d["Resume"].get("Achievements", []) if ach.get("AchievementName")
            ],
            "ProjectNameEmbedding": lambda d: [
                proj.get("ProjectName", "") for proj in d["Resume"].get("Projects", []) if proj.get("ProjectName")
            ],
            "DescriptionEmbedding": lambda d: [
                proj.get("Description", "") for proj in d["Resume"].get("Projects", []) if proj.get("Description")
            ],
            "TechnologiesUsedEmbedding": lambda d: [
                [proj.get("TechnologiesUsed", [])] for proj in d["Resume"].get("Projects", []) if proj.get("TechnologiesUsed")
            ],
            "Pr]jectRoleEmbedding": lambda d: [
                proj.get("Role", "") for proj in d["Resume"].get("Projects", []) if proj.get("Role")
            ],
        }

    def get_embedding(self, text: str) -> Optional[list]:
        """
        Generate embedding for the given text using OpenAI.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector or None if failed
        """
        if not text or not isinstance(text, str) or not text.strip():
            return None
        try:
            response = self.openai_client.embeddings.create(
                input=text.strip(),
                model="text-embedding-3-small"
            )
            return response.data[0].embedding
        except Exception as e:
            self.logger.error(f"Embedding error for '{text[:40]}...': {e}")
            return None

    def flatten_dict(self, d: dict, parent_key: str = '', sep: str = '_') -> dict:
        """
        Flatten a nested dictionary for metadata storage.
        
        Args:
            d: Dictionary to flatten
            parent_key: Parent key prefix
            sep: Separator for nested keys
            
        Returns:
            Flattened dictionary
        """
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self.flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                if all(isinstance(i, dict) for i in v):
                    joined = " | ".join(", ".join(f"{ik}:{iv}" for ik, iv in i.items()) for i in v)
                    items.append((new_key, joined))
                else:
                    items.append((new_key, ", ".join(str(i) for i in v)))
            else:
                items.append((new_key, v))
        return dict(items)

    def process_structured_data(self, structured_data: Dict[str, Any], mongodb_id: ObjectId, original_filename: str = "") -> bool:
        """
        Process structured resume data and store embeddings in ChromaDB.
        
        Args:
            structured_data: The structured resume data from MongoDB
            mongodb_id: MongoDB ObjectId for this document
            original_filename: Original filename for reference
            
        Returns:
            True if successful, False otherwise
        """
        try:
            resume = structured_data.get("Resume", {})
            flat_metadata = self.flatten_dict(resume)
            
            # Create unique resume ID that includes MongoDB ID
            resume_id = f"mongo_{str(mongodb_id)}_{uuid4().hex[:8]}"
            
            # Basic information for metadata
            basic_info = {
                "FullName": resume.get("PersonalInformation", {}).get("FullName", ""),
                "Email": resume.get("PersonalInformation", {}).get("Email", ""),
                "resume_id": resume_id,
                "mongodb_id": str(mongodb_id),  # Include MongoDB ID in metadata
                "original_filename": original_filename
            }
            
            stored_count = 0
            
            # Process each embedding type
            for emb_type, extractor in self.embedding_keys.items():
                text = extractor(structured_data)
                vector = self.get_embedding(text)
                
                if vector:
                    self.collection.add(
                        ids=[f"{resume_id}_{emb_type}"],
                        documents=[text],
                        embeddings=[vector],
                        metadatas=[{
                            **basic_info,
                            "embedding_type": emb_type.replace("Embedding", ""),
                            "source_text": text
                        }]
                    )
                    stored_count += 1
            
            self.logger.info(f"✅ Stored {stored_count} embeddings for MongoDB ID: {mongodb_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error processing structured data for MongoDB ID {mongodb_id}: {e}")
            return False

    def delete_by_mongodb_id(self, mongodb_id: ObjectId) -> bool:
        """
        Delete all ChromaDB entries associated with a MongoDB ID.
        
        Args:
            mongodb_id: MongoDB ObjectId to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Query for all entries with this MongoDB ID
            results = self.collection.get(
                where={"mongodb_id": str(mongodb_id)}
            )
            
            if results and results['ids']:
                # Delete all found entries
                self.collection.delete(ids=results['ids'])
                self.logger.info(f"Deleted {len(results['ids'])} ChromaDB entries for MongoDB ID: {mongodb_id}")
                return True
            else:
                self.logger.info(f"No ChromaDB entries found for MongoDB ID: {mongodb_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error deleting ChromaDB entries for MongoDB ID {mongodb_id}: {e}")
            return False
