"""
ChromaDB Processor Module

This module processes structured resume data and stores embeddings in ChromaDB.
It's designed to work with MongoDB-stored resume data and includes MongoDB IDs in metadata.
"""

import json
from typing import Dict, Any, Optional
from uuid import uuid4
from openai import OpenAI
from chromadb import HttpClient
from bson import ObjectId
import logging

class ChromaDBProcessor:
    """
    A processor that takes structured resume data and stores embeddings in ChromaDB.
    Designed to work with MongoDB ObjectIds and structured data.
    """
    
    def __init__(self, 
                 chroma_host: str = "localhost", 
                 chroma_port: int = 8000,
                 collection_name: str = "resumes_by_type",
                 openai_api_key: Optional[str] = None,
                 openai_base_url: Optional[str] = None):
        """
        Initialize the ChromaDB processor.
        
        Args:
            chroma_host: ChromaDB server host
            chroma_port: ChromaDB server port
            collection_name: ChromaDB collection name
            openai_api_key: OpenAI API key (optional, uses default if None)
            openai_base_url: OpenAI base URL (optional, uses default if None)
        """
        self.chroma_host = chroma_host
        self.chroma_port = chroma_port
        self.collection_name = collection_name
        
        # Initialize OpenAI client
        if openai_api_key and openai_base_url:
            self.openai_client = OpenAI(api_key=openai_api_key, base_url=openai_base_url)
        else:
            self.openai_client = OpenAI()  # Uses default configuration
        
        # Initialize ChromaDB client
        self.chroma_client = HttpClient(host=chroma_host, port=chroma_port)
        self.collection = self.chroma_client.get_or_create_collection(collection_name)
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Define embedding extraction functions that return lists of individual values
        self.embedding_extractors = {
            "FullName": lambda d: [d["Resume"]["PersonalInformation"].get("FullName", "")] if d["Resume"]["PersonalInformation"].get("FullName") else [],

            "Institution": lambda d: [
                edu.get("Institution", "").strip() for edu in d["Resume"].get("Education", [])
                if edu.get("Institution") and edu.get("Institution").strip()
            ],

            "CompanyName": lambda d: [
                exp.get("CompanyName", "").strip() for exp in d["Resume"].get("WorkExperience", [])
                if exp.get("CompanyName") and exp.get("CompanyName").strip()
            ],

            "Role": lambda d: [
                exp.get("Role", "").strip() for exp in d["Resume"].get("WorkExperience", [])
                if exp.get("Role") and exp.get("Role").strip()
            ],

            "Description/Responsibility": lambda d: [
                exp.get("Description/Responsibility", "").strip() for exp in d["Resume"].get("WorkExperience", [])
                if exp.get("Description/Responsibility") and exp.get("Description/Responsibility").strip()
            ],

            "Skills": lambda d: [
                skill.strip() for skill in (
                    d["Resume"].get("Skills", []) if isinstance(d["Resume"].get("Skills", []), list)
                    else [d["Resume"].get("Skills", "")] if d["Resume"].get("Skills") else []
                ) if skill and skill.strip()
            ],

            "CertificationName": lambda d: [
                cert.get("CertificationName", "").strip() for cert in d["Resume"].get("Certifications", [])
                if cert.get("CertificationName") and cert.get("CertificationName").strip()
            ],

            "IssuingOrganization": lambda d: [
                cert.get("IssuingOrganization", "").strip() for cert in d["Resume"].get("Certifications", [])
                if cert.get("IssuingOrganization") and cert.get("IssuingOrganization").strip()
            ],

            "AchievementName": lambda d: [
                ach.get("AchievementName", "").strip() for ach in d["Resume"].get("Achievements", [])
                if ach.get("AchievementName") and ach.get("AchievementName").strip()
            ],

            "ProjectName": lambda d: [
                proj.get("ProjectName", "").strip() for proj in d["Resume"].get("Projects", [])
                if proj.get("ProjectName") and proj.get("ProjectName").strip()
            ],

            "Description": lambda d: [
                proj.get("Description", "").strip() for proj in d["Resume"].get("Projects", [])
                if proj.get("Description") and proj.get("Description").strip()
            ],

            "TechnologiesUsed": lambda d: [
                tech.strip() for proj in d["Resume"].get("Projects", [])
                for tech in (proj.get("TechnologiesUsed", []) if isinstance(proj.get("TechnologiesUsed", []), list) else [])
                if tech and tech.strip()
            ],

            "ProjectRole": lambda d: [
                proj.get("Role", "").strip() for proj in d["Resume"].get("Projects", [])
                if proj.get("Role") and proj.get("Role").strip()
            ],
        }

    def get_embedding(self, text: str) -> Optional[list]:
        """
        Generate embedding for the given text using OpenAI.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector or None if failed
        """
        if not text or not isinstance(text, str) or not text.strip():
            return None
        try:
            response = self.openai_client.embeddings.create(
                input=text.strip(),
                model="text-embedding-3-small"
            )
            return response.data[0].embedding
        except Exception as e:
            self.logger.error(f"Embedding error for '{text[:40]}...': {e}")
            return None

    def flatten_dict(self, d: dict, parent_key: str = '', sep: str = '_') -> dict:
        """
        Flatten a nested dictionary for metadata storage.
        
        Args:
            d: Dictionary to flatten
            parent_key: Parent key prefix
            sep: Separator for nested keys
            
        Returns:
            Flattened dictionary
        """
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self.flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                if all(isinstance(i, dict) for i in v):
                    joined = " | ".join(", ".join(f"{ik}:{iv}" for ik, iv in i.items()) for i in v)
                    items.append((new_key, joined))
                else:
                    items.append((new_key, ", ".join(str(i) for i in v)))
            else:
                items.append((new_key, v))
        return dict(items)

    def process_structured_data(self, structured_data: Dict[str, Any], mongodb_id: ObjectId, original_filename: str = "") -> bool:
        """
        Process structured resume data and store separate embeddings for each individual value.

        Args:
            structured_data: The structured resume data from MongoDB
            mongodb_id: MongoDB ObjectId for this document
            original_filename: Original filename for reference

        Returns:
            True if successful, False otherwise
        """
        try:
            resume = structured_data.get("Resume", {})

            # Create unique resume ID that includes MongoDB ID
            resume_id = f"mongo_{str(mongodb_id)}_{uuid4().hex[:8]}"

            # Basic information for metadata
            basic_info = {
                "FullName": resume.get("PersonalInformation", {}).get("FullName", ""),
                "Email": resume.get("PersonalInformation", {}).get("Email", ""),
                "resume_id": resume_id,
                "mongodb_id": str(mongodb_id),  # Include MongoDB ID in metadata
                "original_filename": original_filename
            }

            stored_count = 0

            # Process each embedding type - create separate embeddings for each individual value
            for emb_type, extractor in self.embedding_extractors.items():
                values = extractor(structured_data)

                # Create separate embedding for each individual value
                for i, value in enumerate(values):
                    if value and value.strip():  # Only process non-empty values
                        vector = self.get_embedding(value)

                        if vector:
                            # Create unique ID for each individual embedding
                            embedding_id = f"{resume_id}_{emb_type}_{i}"

                            self.collection.add(
                                ids=[embedding_id],
                                documents=[value],
                                embeddings=[vector],
                                metadatas=[{
                                    **basic_info,
                                    "embedding_type": emb_type,
                                    "source_text": value,
                                    "value_index": i  # Track which value this is (e.g., 2nd company, 3rd skill)
                                }]
                            )
                            stored_count += 1

            self.logger.info(f"✅ Stored {stored_count} individual embeddings for MongoDB ID: {mongodb_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error processing structured data for MongoDB ID {mongodb_id}: {e}")
            return False

    def delete_by_mongodb_id(self, mongodb_id: ObjectId) -> bool:
        """
        Delete all ChromaDB entries associated with a MongoDB ID.
        
        Args:
            mongodb_id: MongoDB ObjectId to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Query for all entries with this MongoDB ID
            results = self.collection.get(
                where={"mongodb_id": str(mongodb_id)}
            )
            
            if results and results['ids']:
                # Delete all found entries
                self.collection.delete(ids=results['ids'])
                self.logger.info(f"Deleted {len(results['ids'])} ChromaDB entries for MongoDB ID: {mongodb_id}")
                return True
            else:
                self.logger.info(f"No ChromaDB entries found for MongoDB ID: {mongodb_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error deleting ChromaDB entries for MongoDB ID {mongodb_id}: {e}")
            return False
